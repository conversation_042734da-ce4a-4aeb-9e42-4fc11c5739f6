-- For new changelog see CHANGELOG.md

v0.4.4  --  2017.03.15
* Expose _get and _has methods. It provides temporary means for better cache inspection until
  v1 is delivered

v0.4.3  --  2017.02.20
* Fix normalization resolver, so it validates already normalized resolvers (reported at #70)

v0.4.2  --  2017.02.17
* Guard proper promise resolution in promise mode
* Improve documentation
* Improve npm scripts configuration
* Improve CI configuration

v0.4.1  --  2016.07.13
* Fix promise mode to not use `then` and `finally` pair. Thanks @Kovensky
* Add 'done' mode to 'promise' mode (so now callbacks can be registered through `done` but no
  `finally` event if it's implemented)

v0.4.0  --  2016.07.07
* Introduce 'promise' mode
* Change signature of internal events for 'async' case
* Document 'normalizer' option

v0.3.10  --  2016.04.21
* Fix resolvers bug (#12)
* Fix arguments support for weak mode
* Update repository address after rename

v0.3.9  --  2015.08.04
* Update dependencies
* Improve documentation
* FIx spelling of LICENSE

v0.3.8  --  2014.10.08
* Introduce WeakMap based `weak` mode

v0.3.7  --  2014.08.14
* Fix prefetch support for asynchronous functions (#19)
* Configure lint scripts

v0.3.6  --  2014.07.28
* Fix race condition issue related to clear/delete calls and returning id's (#18)
* Fix maxAge major cache handling issue (on timeout instead of individual record whole cache was
  cleared)

v0.3.5  --  2014.07.07
* Fix internal id genaration, for primitive, length = 1 case.
  Ids were not serialized to strings, and that caused issues with other
  internal logiv, which e.g. treated `null` case specifically.
  Fixes #15 (Thanks @isaacg for reporting)

v0.3.4  --  2014.06.22
* Fix async handling in case of clear between two async calls
  (assurance of unique cache ids solves that). Fixes #13

v0.3.3  --  2014.05.12
* Fix profiler special property definition

v0.3.2  --  2014.05.01
* Provide no-arguments memoization out of a box
  (no need to provide normalizer to `memoizee/plain`)

v0.3.1  --  2014.04.27
* Update package.json to use latest 'tad' engine (fixes tests evaluation)

v0.3.0  --  2014.04.27
Major reorganization and partial refactoring
* Move out main modules from lib folder
* Introduce normalizer based memoization. Primitive and regular handlers were converted into
  dedicated normalizers (located in lib/normalizers folder). Custom normalizers can be provided at
  run time via `normalizer` option.
* Provide 'plain' module which does not import (require) any extensions or normalizers. Thanks to
  that it's lighter for e.g. client-side bundle. Any extensions that are expected to be used should
	be required upfront, normalizers should be provided via `normalizer` option.
* Rename `memoized.clear` into `memoized.delete`, and `memoized.clearAll` into `memoized.clear`
* Rename `memoized.clearRef` into `memoized.deleteRef` (applies to 'refCounter' option)
* Remove 'method' option. Instead 'methods' and 'methods-plan' modules are provided which generate
  descriptors for lazy created memoized methods.
* 'profile' is no longer an extension. It's provided as dedicated module.
* Clean up logic for `async` handling
* Take out 'max' extension's LRU logic into external `lru-queue` package.
  See https://github.com/medikoo/lru-queue
* Remove possibility to access original arguments when resolvers are used
* Assure expected length of memoized functions
* Remove Makefile (it's environment agnostic package)

v0.2.6  --  2013.10.08
* Fix internal events propagation when handling async calls that
  resolve with errors. `asyncpurge` was emitted for values that had no
	`asyncinit` emitted. Issue #9

v0.2.5  --  2013.06.21
* Fix primitive handling for dynamic arguments length

v0.2.4  --  2013.03.23
* Throw on circular invocations, they cannot be memoized as intended.

v0.2.3  --  2012.10.04
* Fixed serious bug related to not properly cleared cache when working in
  regular mode

v0.2.2  --  2012.10.03
* preFetch functionality for maxAge variant
* Prevent memoization of already memoized functions

v0.2.1  --  2012.09.21
* Fix missing global reference in method option logic
* Fix variable visibility in async option logic
* Lint cleanup

v0.2.0  --  2012.09.21
* Modularization and general algorithm cleanup
* Cache is limited (max option) using LRU instead of FIFO algorithm
* Improved async mode, and its handling by other options

v0.1.1  --  2012.09.19
* Fix dispose invocations for no arguments call
* Small documentation improvements

v0.1.0  --  2012.09.18
* Initial. Derived from es5-ext package and added `async`, `maxAge`,
  `refCounter`, `max` and `dispose` options.

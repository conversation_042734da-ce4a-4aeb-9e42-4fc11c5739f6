{"name": "commander", "version": "5.1.0", "description": "the complete solution for node.js command-line programs", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/commander.js.git"}, "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "typescript-lint": "eslint typings/*.ts", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "main": "index", "files": ["index.js", "typings/index.d.ts"], "dependencies": {}, "devDependencies": {"@types/jest": "^25.2.1", "@types/node": "^12.12.36", "@typescript-eslint/eslint-plugin": "^2.29.0", "eslint": "^6.8.0", "eslint-config-standard-with-typescript": "^15.0.1", "eslint-plugin-jest": "^23.8.2", "jest": "^25.4.0", "standard": "^14.3.3", "typescript": "^3.7.5"}, "typings": "typings/index.d.ts", "engines": {"node": ">= 6"}}
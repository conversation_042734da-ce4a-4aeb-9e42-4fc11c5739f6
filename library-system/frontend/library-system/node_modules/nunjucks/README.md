# Nunjucks

[![NPM Version][npm-image]][npm-url]
[![NPM Downloads][downloads-image]][downloads-url]
[![Linux Build][github-actions-image]][github-actions-url]
[![Windows Build][appveyor-image]][appveyor-url]
[![Test Codecov][codecov-image]][codecov-url]

[Nunjucks](https://mozilla.github.io/nunjucks/) is a full featured
templating engine for javascript. It is heavily inspired by
[jinja2](http://jinja.pocoo.org/). View the docs
[here](https://mozilla.github.io/nunjucks/).

## Installation

`npm install nunjucks`

To use the file watcher built-in to Nunjucks, Cho<PERSON>dar must be installed separately.

`npm install nunjucks chokidar`

(View the [CHANGELOG](https://github.com/mozilla/nunjucks/releases))

## Documentation

See [here](https://mozilla.github.io/nunjucks/).

## Browser Support

Supported in all modern browsers. For IE8 support, use [es5-shim](https://github.com/es-shims/es5-shim).

## Tests

Run the tests with `npm test`.

Watch `master` branch's [tests running in the browser](https://mozilla.github.io/nunjucks/files/tests/browser/).

## Mailing List

Join our mailing list and get help with and issues you have:
https://groups.google.com/forum/?fromgroups#!forum/nunjucks

## Want to help?

Contributions are always welcome! Before you submit an issue or pull request, please read our [contribution guidelines](CONTRIBUTING.md).

[Contributors](https://github.com/mozilla/nunjucks/graphs/contributors)

[npm-image]: https://img.shields.io/npm/v/nunjucks.svg
[npm-url]: https://npmjs.org/package/nunjucks
[downloads-image]: https://img.shields.io/npm/dm/nunjucks.svg
[downloads-url]: https://npmjs.org/package/nunjucks
[github-actions-image]: https://img.shields.io/github/workflow/status/mozilla/nunjucks/Tests/master.svg?label=linux
[github-actions-url]: https://github.com/mozilla/nunjucks/actions
[appveyor-image]: https://img.shields.io/appveyor/ci/fdintino/nunjucks/master.svg?label=windows
[appveyor-url]: https://ci.appveyor.com/project/fdintino/nunjucks
[codecov-image]: https://img.shields.io/codecov/c/gh/mozilla/nunjucks.svg
[codecov-url]: https://codecov.io/gh/mozilla/nunjucks/branch/master

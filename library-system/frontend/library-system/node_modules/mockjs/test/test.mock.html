<!DOCTYPE html>
<html>
<head>
    <title>Test: Mock</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://unpkg.com/mocha@3.x/mocha.css" rel="stylesheet">
    <style type="text/css">
        li.test h2 {
            font-family: monospace;
            /*white-space: pre;*/
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div id="mocha"></div>
    <!-- Include local Mocha. -->
    <script src="https://unpkg.com/mocha@3.x/mocha.js"></script>
    <script src="https://unpkg.com/chai@4.2.0/chai.js"></script>
    <script>mocha.setup('bdd')</script>
    <!-- Include RequireJS -->
    <script src="https://unpkg.com/requirejs@2.x/require.js"></script>
    <script type="text/javascript">
        window.require = window.requirejs;
        require.config({
            paths: {
                'mock': '../dist/mock',
                jquery: 'https://unpkg.com/jquery@2.2.4/dist/jquery',
                underscore: 'https://unpkg.com/underscore@1.9.1/underscore'
            }
        })
    </script>

    <hr>
    <div id="container" style="margin: 60px 50px;"></div>
    <pre id="tpl" class="container"></pre>
    <pre id="data" class="container"></pre>

    <!-- Include the actual tests. -->
    <script src="./test.mock.spec.dtd.js"></script>
    <script src="./test.mock.spec.dpd.js"></script>
    <script src="./test.mock.random.js"></script>
    <script src="./test.mock.mock.js"></script>
    <script src="./test.mock.request.js"></script>
    <script src="./test.mock.schema.js"></script>
    <script src="./test.mock.valid.js"></script>
    
    <!-- PhantomJS -->
    <script type="text/javascript">
         if (window.mochaPhantomJS) {
             mochaPhantomJS.run()
         } else {
             mocha.run()
         }
    </script>
</body>
</html>
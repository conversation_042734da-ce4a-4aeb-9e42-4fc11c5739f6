{"name": "@umijs/openapi", "version": "1.13.15", "description": "", "repository": {"type": "git", "url": "<EMAIL>:chenshuai2144/openapi2typescript.git"}, "license": "MIT", "author": "chenshuai2144/kobe", "main": "dist/index.js", "bin": {"openapi2ts": "dist/cli.js"}, "files": ["dist", "templates"], "scripts": {"build": "tsc", "localConvert4Project": "rm -rf ./test/servers/ ./test/file-servers/ &&  npm run build && cd ./test && node ./test.js && cd .. && rm -rf /Users/<USER>/wj/psp-web-pro/src/services/wj && mv ./test/servers/api/  /Users/<USER>/wj/psp-web-pro/src/services/wj", "prepublishOnly": "npm run build && np --no-cleanup --yolo --no-publish --any-branch", "start": "tsc -w", "test": "rm -rf ./test/servers/ ./test/servers-allof/ ./test/file-servers/ &&  npm run build && cd ./test && node ./test.js && cd ..", "test:windows": "rimraf ./test/servers/ ./test/servers-allof/ ./test/file-servers/ && npm run build && cd ./test && ts-node ./test.js --project tsconfig.json && cd .."}, "dependencies": {"chalk": "^4.1.2", "cosmiconfig": "^9.0.0", "dayjs": "^1.10.3", "glob": "^7.1.6", "lodash": "^4.17.21", "memoizee": "^0.4.15", "mock.js": "^0.2.0", "mockjs": "^1.1.0", "node-fetch": "^2.6.1", "number-to-words": "^1.2.4", "nunjucks": "^3.2.2", "openapi3-ts": "^2.0.1", "prettier": "^2.2.1", "reserved-words": "^0.1.2", "rimraf": "^3.0.2", "swagger2openapi": "^7.0.4", "tiny-pinyin": "^1.3.2"}, "devDependencies": {"@types/express": "^4.17.11", "@types/node": "^14.14.22", "@types/node-fetch": "^2.6.12", "@types/number-to-words": "^1.2.3", "@types/swagger2openapi": "^7.0.4", "np": "^7.2.0", "ts-node": "^10.9.2", "tslib": "^2.6.0", "typescript": "^4.1.3"}, "packageManager": "pnpm@9.13.2+sha512.88c9c3864450350e65a33587ab801acf946d7c814ed1134da4a924f6df5a2120fd36b46aab68f7cd1d413149112d53c7db3a4136624cfd00ff1846a0c6cef48a"}
#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const chalk_1 = tslib_1.__importDefault(require("chalk"));
const cosmiconfig_1 = require("cosmiconfig");
const index_1 = require("./index");
const explorerSync = cosmiconfig_1.cosmiconfigSync('openapi2ts');
const searchedFor = explorerSync.search();
function run() {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        try {
            if (searchedFor === null || searchedFor === void 0 ? void 0 : searchedFor.config) {
                const configs = Array.isArray(searchedFor.config)
                    ? searchedFor.config
                    : [searchedFor.config];
                for (const config of configs) {
                    yield index_1.generateService(config);
                }
            }
            else {
                throw new Error('config is not found');
            }
        }
        catch (error) {
            console.log(chalk_1.default.red(error));
        }
    });
}
run();

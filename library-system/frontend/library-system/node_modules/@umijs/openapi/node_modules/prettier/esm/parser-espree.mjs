var E=(a,u)=>()=>(u||a((u={exports:{}}).exports,u),u.exports);var oe=E((Qh,zr)=>{var Ye=function(a){return a&&a.Math==Math&&a};zr.exports=Ye(typeof globalThis=="object"&&globalThis)||Ye(typeof window=="object"&&window)||Ye(typeof self=="object"&&self)||Ye(typeof global=="object"&&global)||function(){return this}()||Function("return this")()});var me=E(($h,Gr)=>{Gr.exports=function(a){try{return!!a()}catch{return!0}}});var xe=E((Yh,Hr)=>{var fn=me();Hr.exports=!fn(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!=7})});var bt=E((Zh,Kr)=>{var dn=me();Kr.exports=!dn(function(){var a=function(){}.bind();return typeof a!="function"||a.hasOwnProperty("prototype")})});var et=E((el,Xr)=>{var mn=bt(),Ze=Function.prototype.call;Xr.exports=mn?Ze.bind(Ze):function(){return Ze.apply(Ze,arguments)}});var Yr=E($r=>{"use strict";var Jr={}.propertyIsEnumerable,Qr=Object.getOwnPropertyDescriptor,vn=Qr&&!Jr.call({1:2},1);$r.f=vn?function(u){var o=Qr(this,u);return!!o&&o.enumerable}:Jr});var _t=E((rl,Zr)=>{Zr.exports=function(a,u){return{enumerable:!(a&1),configurable:!(a&2),writable:!(a&4),value:u}}});var ye=E((il,ri)=>{var ei=bt(),ti=Function.prototype,St=ti.call,gn=ei&&ti.bind.bind(St,St);ri.exports=ei?gn:function(a){return function(){return St.apply(a,arguments)}}});var ai=E((sl,si)=>{var ii=ye(),xn=ii({}.toString),yn=ii("".slice);si.exports=function(a){return yn(xn(a),8,-1)}});var ui=E((al,ni)=>{var An=ye(),Cn=me(),En=ai(),wt=Object,bn=An("".split);ni.exports=Cn(function(){return!wt("z").propertyIsEnumerable(0)})?function(a){return En(a)=="String"?bn(a,""):wt(a)}:wt});var kt=E((nl,oi)=>{oi.exports=function(a){return a==null}});var Ft=E((ul,hi)=>{var _n=kt(),Sn=TypeError;hi.exports=function(a){if(_n(a))throw Sn("Can't call method on "+a);return a}});var tt=E((ol,li)=>{var wn=ui(),kn=Ft();li.exports=function(a){return wn(kn(a))}});var It=E((hl,ci)=>{var Bt=typeof document=="object"&&document.all,Fn=typeof Bt>"u"&&Bt!==void 0;ci.exports={all:Bt,IS_HTMLDDA:Fn}});var le=E((ll,fi)=>{var pi=It(),Bn=pi.all;fi.exports=pi.IS_HTMLDDA?function(a){return typeof a=="function"||a===Bn}:function(a){return typeof a=="function"}});var Pe=E((cl,vi)=>{var di=le(),mi=It(),In=mi.all;vi.exports=mi.IS_HTMLDDA?function(a){return typeof a=="object"?a!==null:di(a)||a===In}:function(a){return typeof a=="object"?a!==null:di(a)}});var rt=E((pl,gi)=>{var Tt=oe(),Tn=le(),Pn=function(a){return Tn(a)?a:void 0};gi.exports=function(a,u){return arguments.length<2?Pn(Tt[a]):Tt[a]&&Tt[a][u]}});var yi=E((fl,xi)=>{var Dn=ye();xi.exports=Dn({}.isPrototypeOf)});var Ci=E((dl,Ai)=>{var Nn=rt();Ai.exports=Nn("navigator","userAgent")||""});var Fi=E((ml,ki)=>{var wi=oe(),Pt=Ci(),Ei=wi.process,bi=wi.Deno,_i=Ei&&Ei.versions||bi&&bi.version,Si=_i&&_i.v8,ce,it;Si&&(ce=Si.split("."),it=ce[0]>0&&ce[0]<4?1:+(ce[0]+ce[1]));!it&&Pt&&(ce=Pt.match(/Edge\/(\d+)/),(!ce||ce[1]>=74)&&(ce=Pt.match(/Chrome\/(\d+)/),ce&&(it=+ce[1])));ki.exports=it});var Dt=E((vl,Ii)=>{var Bi=Fi(),On=me();Ii.exports=!!Object.getOwnPropertySymbols&&!On(function(){var a=Symbol();return!String(a)||!(Object(a)instanceof Symbol)||!Symbol.sham&&Bi&&Bi<41})});var Nt=E((gl,Ti)=>{var Ln=Dt();Ti.exports=Ln&&!Symbol.sham&&typeof Symbol.iterator=="symbol"});var Ot=E((xl,Pi)=>{var Vn=rt(),Rn=le(),jn=yi(),qn=Nt(),Mn=Object;Pi.exports=qn?function(a){return typeof a=="symbol"}:function(a){var u=Vn("Symbol");return Rn(u)&&jn(u.prototype,Mn(a))}});var Ni=E((yl,Di)=>{var Un=String;Di.exports=function(a){try{return Un(a)}catch{return"Object"}}});var Li=E((Al,Oi)=>{var Wn=le(),zn=Ni(),Gn=TypeError;Oi.exports=function(a){if(Wn(a))return a;throw Gn(zn(a)+" is not a function")}});var Ri=E((Cl,Vi)=>{var Hn=Li(),Kn=kt();Vi.exports=function(a,u){var o=a[u];return Kn(o)?void 0:Hn(o)}});var qi=E((El,ji)=>{var Lt=et(),Vt=le(),Rt=Pe(),Xn=TypeError;ji.exports=function(a,u){var o,l;if(u==="string"&&Vt(o=a.toString)&&!Rt(l=Lt(o,a))||Vt(o=a.valueOf)&&!Rt(l=Lt(o,a))||u!=="string"&&Vt(o=a.toString)&&!Rt(l=Lt(o,a)))return l;throw Xn("Can't convert object to primitive value")}});var Ui=E((bl,Mi)=>{Mi.exports=!1});var st=E((_l,zi)=>{var Wi=oe(),Jn=Object.defineProperty;zi.exports=function(a,u){try{Jn(Wi,a,{value:u,configurable:!0,writable:!0})}catch{Wi[a]=u}return u}});var at=E((Sl,Hi)=>{var Qn=oe(),$n=st(),Gi="__core-js_shared__",Yn=Qn[Gi]||$n(Gi,{});Hi.exports=Yn});var jt=E((wl,Xi)=>{var Zn=Ui(),Ki=at();(Xi.exports=function(a,u){return Ki[a]||(Ki[a]=u!==void 0?u:{})})("versions",[]).push({version:"3.26.1",mode:Zn?"pure":"global",copyright:"\xA9 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.26.1/LICENSE",source:"https://github.com/zloirock/core-js"})});var Qi=E((kl,Ji)=>{var eu=Ft(),tu=Object;Ji.exports=function(a){return tu(eu(a))}});var be=E((Fl,$i)=>{var ru=ye(),iu=Qi(),su=ru({}.hasOwnProperty);$i.exports=Object.hasOwn||function(u,o){return su(iu(u),o)}});var qt=E((Bl,Yi)=>{var au=ye(),nu=0,uu=Math.random(),ou=au(1 .toString);Yi.exports=function(a){return"Symbol("+(a===void 0?"":a)+")_"+ou(++nu+uu,36)}});var ss=E((Il,is)=>{var hu=oe(),lu=jt(),Zi=be(),cu=qt(),es=Dt(),rs=Nt(),De=lu("wks"),we=hu.Symbol,ts=we&&we.for,pu=rs?we:we&&we.withoutSetter||cu;is.exports=function(a){if(!Zi(De,a)||!(es||typeof De[a]=="string")){var u="Symbol."+a;es&&Zi(we,a)?De[a]=we[a]:rs&&ts?De[a]=ts(u):De[a]=pu(u)}return De[a]}});var os=E((Tl,us)=>{var fu=et(),as=Pe(),ns=Ot(),du=Ri(),mu=qi(),vu=ss(),gu=TypeError,xu=vu("toPrimitive");us.exports=function(a,u){if(!as(a)||ns(a))return a;var o=du(a,xu),l;if(o){if(u===void 0&&(u="default"),l=fu(o,a,u),!as(l)||ns(l))return l;throw gu("Can't convert object to primitive value")}return u===void 0&&(u="number"),mu(a,u)}});var Mt=E((Pl,hs)=>{var yu=os(),Au=Ot();hs.exports=function(a){var u=yu(a,"string");return Au(u)?u:u+""}});var ps=E((Dl,cs)=>{var Cu=oe(),ls=Pe(),Ut=Cu.document,Eu=ls(Ut)&&ls(Ut.createElement);cs.exports=function(a){return Eu?Ut.createElement(a):{}}});var Wt=E((Nl,fs)=>{var bu=xe(),_u=me(),Su=ps();fs.exports=!bu&&!_u(function(){return Object.defineProperty(Su("div"),"a",{get:function(){return 7}}).a!=7})});var zt=E(ms=>{var wu=xe(),ku=et(),Fu=Yr(),Bu=_t(),Iu=tt(),Tu=Mt(),Pu=be(),Du=Wt(),ds=Object.getOwnPropertyDescriptor;ms.f=wu?ds:function(u,o){if(u=Iu(u),o=Tu(o),Du)try{return ds(u,o)}catch{}if(Pu(u,o))return Bu(!ku(Fu.f,u,o),u[o])}});var gs=E((Ll,vs)=>{var Nu=xe(),Ou=me();vs.exports=Nu&&Ou(function(){return Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype!=42})});var nt=E((Vl,xs)=>{var Lu=Pe(),Vu=String,Ru=TypeError;xs.exports=function(a){if(Lu(a))return a;throw Ru(Vu(a)+" is not an object")}});var Me=E(As=>{var ju=xe(),qu=Wt(),Mu=gs(),ut=nt(),ys=Mt(),Uu=TypeError,Gt=Object.defineProperty,Wu=Object.getOwnPropertyDescriptor,Ht="enumerable",Kt="configurable",Xt="writable";As.f=ju?Mu?function(u,o,l){if(ut(u),o=ys(o),ut(l),typeof u=="function"&&o==="prototype"&&"value"in l&&Xt in l&&!l[Xt]){var v=Wu(u,o);v&&v[Xt]&&(u[o]=l.value,l={configurable:Kt in l?l[Kt]:v[Kt],enumerable:Ht in l?l[Ht]:v[Ht],writable:!1})}return Gt(u,o,l)}:Gt:function(u,o,l){if(ut(u),o=ys(o),ut(l),qu)try{return Gt(u,o,l)}catch{}if("get"in l||"set"in l)throw Uu("Accessors not supported");return"value"in l&&(u[o]=l.value),u}});var Jt=E((jl,Cs)=>{var zu=xe(),Gu=Me(),Hu=_t();Cs.exports=zu?function(a,u,o){return Gu.f(a,u,Hu(1,o))}:function(a,u,o){return a[u]=o,a}});var _s=E((ql,bs)=>{var Qt=xe(),Ku=be(),Es=Function.prototype,Xu=Qt&&Object.getOwnPropertyDescriptor,$t=Ku(Es,"name"),Ju=$t&&function(){}.name==="something",Qu=$t&&(!Qt||Qt&&Xu(Es,"name").configurable);bs.exports={EXISTS:$t,PROPER:Ju,CONFIGURABLE:Qu}});var ws=E((Ml,Ss)=>{var $u=ye(),Yu=le(),Yt=at(),Zu=$u(Function.toString);Yu(Yt.inspectSource)||(Yt.inspectSource=function(a){return Zu(a)});Ss.exports=Yt.inspectSource});var Bs=E((Ul,Fs)=>{var eo=oe(),to=le(),ks=eo.WeakMap;Fs.exports=to(ks)&&/native code/.test(String(ks))});var Ps=E((Wl,Ts)=>{var ro=jt(),io=qt(),Is=ro("keys");Ts.exports=function(a){return Is[a]||(Is[a]=io(a))}});var Zt=E((zl,Ds)=>{Ds.exports={}});var Vs=E((Gl,Ls)=>{var so=Bs(),Os=oe(),ao=Pe(),no=Jt(),er=be(),tr=at(),uo=Ps(),oo=Zt(),Ns="Object already initialized",rr=Os.TypeError,ho=Os.WeakMap,ot,Ue,ht,lo=function(a){return ht(a)?Ue(a):ot(a,{})},co=function(a){return function(u){var o;if(!ao(u)||(o=Ue(u)).type!==a)throw rr("Incompatible receiver, "+a+" required");return o}};so||tr.state?(pe=tr.state||(tr.state=new ho),pe.get=pe.get,pe.has=pe.has,pe.set=pe.set,ot=function(a,u){if(pe.has(a))throw rr(Ns);return u.facade=a,pe.set(a,u),u},Ue=function(a){return pe.get(a)||{}},ht=function(a){return pe.has(a)}):(ke=uo("state"),oo[ke]=!0,ot=function(a,u){if(er(a,ke))throw rr(Ns);return u.facade=a,no(a,ke,u),u},Ue=function(a){return er(a,ke)?a[ke]:{}},ht=function(a){return er(a,ke)});var pe,ke;Ls.exports={set:ot,get:Ue,has:ht,enforce:lo,getterFor:co}});var sr=E((Hl,js)=>{var po=me(),fo=le(),lt=be(),ir=xe(),mo=_s().CONFIGURABLE,vo=ws(),Rs=Vs(),go=Rs.enforce,xo=Rs.get,ct=Object.defineProperty,yo=ir&&!po(function(){return ct(function(){},"length",{value:8}).length!==8}),Ao=String(String).split("String"),Co=js.exports=function(a,u,o){String(u).slice(0,7)==="Symbol("&&(u="["+String(u).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),o&&o.getter&&(u="get "+u),o&&o.setter&&(u="set "+u),(!lt(a,"name")||mo&&a.name!==u)&&(ir?ct(a,"name",{value:u,configurable:!0}):a.name=u),yo&&o&&lt(o,"arity")&&a.length!==o.arity&&ct(a,"length",{value:o.arity});try{o&&lt(o,"constructor")&&o.constructor?ir&&ct(a,"prototype",{writable:!1}):a.prototype&&(a.prototype=void 0)}catch{}var l=go(a);return lt(l,"source")||(l.source=Ao.join(typeof u=="string"?u:"")),a};Function.prototype.toString=Co(function(){return fo(this)&&xo(this).source||vo(this)},"toString")});var Ms=E((Kl,qs)=>{var Eo=le(),bo=Me(),_o=sr(),So=st();qs.exports=function(a,u,o,l){l||(l={});var v=l.enumerable,b=l.name!==void 0?l.name:u;if(Eo(o)&&_o(o,b,l),l.global)v?a[u]=o:So(u,o);else{try{l.unsafe?a[u]&&(v=!0):delete a[u]}catch{}v?a[u]=o:bo.f(a,u,{value:o,enumerable:!1,configurable:!l.nonConfigurable,writable:!l.nonWritable})}return a}});var Ws=E((Xl,Us)=>{var wo=Math.ceil,ko=Math.floor;Us.exports=Math.trunc||function(u){var o=+u;return(o>0?ko:wo)(o)}});var ar=E((Jl,zs)=>{var Fo=Ws();zs.exports=function(a){var u=+a;return u!==u||u===0?0:Fo(u)}});var Hs=E((Ql,Gs)=>{var Bo=ar(),Io=Math.max,To=Math.min;Gs.exports=function(a,u){var o=Bo(a);return o<0?Io(o+u,0):To(o,u)}});var Xs=E(($l,Ks)=>{var Po=ar(),Do=Math.min;Ks.exports=function(a){return a>0?Do(Po(a),9007199254740991):0}});var Qs=E((Yl,Js)=>{var No=Xs();Js.exports=function(a){return No(a.length)}});var Zs=E((Zl,Ys)=>{var Oo=tt(),Lo=Hs(),Vo=Qs(),$s=function(a){return function(u,o,l){var v=Oo(u),b=Vo(v),y=Lo(l,b),I;if(a&&o!=o){for(;b>y;)if(I=v[y++],I!=I)return!0}else for(;b>y;y++)if((a||y in v)&&v[y]===o)return a||y||0;return!a&&-1}};Ys.exports={includes:$s(!0),indexOf:$s(!1)}});var ra=E((ec,ta)=>{var Ro=ye(),nr=be(),jo=tt(),qo=Zs().indexOf,Mo=Zt(),ea=Ro([].push);ta.exports=function(a,u){var o=jo(a),l=0,v=[],b;for(b in o)!nr(Mo,b)&&nr(o,b)&&ea(v,b);for(;u.length>l;)nr(o,b=u[l++])&&(~qo(v,b)||ea(v,b));return v}});var sa=E((tc,ia)=>{ia.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]});var na=E(aa=>{var Uo=ra(),Wo=sa(),zo=Wo.concat("length","prototype");aa.f=Object.getOwnPropertyNames||function(u){return Uo(u,zo)}});var oa=E(ua=>{ua.f=Object.getOwnPropertySymbols});var la=E((sc,ha)=>{var Go=rt(),Ho=ye(),Ko=na(),Xo=oa(),Jo=nt(),Qo=Ho([].concat);ha.exports=Go("Reflect","ownKeys")||function(u){var o=Ko.f(Jo(u)),l=Xo.f;return l?Qo(o,l(u)):o}});var fa=E((ac,pa)=>{var ca=be(),$o=la(),Yo=zt(),Zo=Me();pa.exports=function(a,u,o){for(var l=$o(u),v=Zo.f,b=Yo.f,y=0;y<l.length;y++){var I=l[y];!ca(a,I)&&!(o&&ca(o,I))&&v(a,I,b(u,I))}}});var ma=E((nc,da)=>{var eh=me(),th=le(),rh=/#|\.prototype\./,We=function(a,u){var o=sh[ih(a)];return o==nh?!0:o==ah?!1:th(u)?eh(u):!!u},ih=We.normalize=function(a){return String(a).replace(rh,".").toLowerCase()},sh=We.data={},ah=We.NATIVE="N",nh=We.POLYFILL="P";da.exports=We});var ga=E((uc,va)=>{var ur=oe(),uh=zt().f,oh=Jt(),hh=Ms(),lh=st(),ch=fa(),ph=ma();va.exports=function(a,u){var o=a.target,l=a.global,v=a.stat,b,y,I,T,x,R;if(l?y=ur:v?y=ur[o]||lh(o,{}):y=(ur[o]||{}).prototype,y)for(I in u){if(x=u[I],a.dontCallGetSet?(R=uh(y,I),T=R&&R.value):T=y[I],b=ph(l?I:o+(v?".":"#")+I,a.forced),!b&&T!==void 0){if(typeof x==typeof T)continue;ch(x,T)}(a.sham||T&&T.sham)&&oh(x,"sham",!0),hh(y,I,x,a)}}});var xa=E(()=>{var fh=ga(),or=oe();fh({global:!0,forced:or.globalThis!==or},{globalThis:or})});var Ca=E((lc,Aa)=>{var ya=sr(),dh=Me();Aa.exports=function(a,u,o){return o.get&&ya(o.get,u,{getter:!0}),o.set&&ya(o.set,u,{setter:!0}),dh.f(a,u,o)}});var ba=E((cc,Ea)=>{"use strict";var mh=nt();Ea.exports=function(){var a=mh(this),u="";return a.hasIndices&&(u+="d"),a.global&&(u+="g"),a.ignoreCase&&(u+="i"),a.multiline&&(u+="m"),a.dotAll&&(u+="s"),a.unicode&&(u+="u"),a.unicodeSets&&(u+="v"),a.sticky&&(u+="y"),u}});xa();var vh=oe(),gh=xe(),xh=Ca(),yh=ba(),Ah=me(),_a=vh.RegExp,Sa=_a.prototype,Ch=gh&&Ah(function(){var a=!0;try{_a(".","d")}catch{a=!1}var u={},o="",l=a?"dgimsy":"gimsy",v=function(T,x){Object.defineProperty(u,T,{get:function(){return o+=x,!0}})},b={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};a&&(b.hasIndices="d");for(var y in b)v(y,b[y]);var I=Object.getOwnPropertyDescriptor(Sa,"flags").get.call(u);return I!==l||o!==l});Ch&&xh(Sa,"flags",{configurable:!0,get:yh});var pr=Object.defineProperty,Eh=Object.getOwnPropertyDescriptor,fr=Object.getOwnPropertyNames,bh=Object.prototype.hasOwnProperty,wa=(a,u)=>function(){return a&&(u=(0,a[fr(a)[0]])(a=0)),u},Q=(a,u)=>function(){return u||(0,a[fr(a)[0]])((u={exports:{}}).exports,u),u.exports},_h=(a,u)=>{for(var o in u)pr(a,o,{get:u[o],enumerable:!0})},Sh=(a,u,o,l)=>{if(u&&typeof u=="object"||typeof u=="function")for(let v of fr(u))!bh.call(a,v)&&v!==o&&pr(a,v,{get:()=>u[v],enumerable:!(l=Eh(u,v))||l.enumerable});return a},wh=a=>Sh(pr({},"__esModule",{value:!0}),a),J=wa({"<define:process>"(){}}),dr=Q({"src/common/parser-create-error.js"(a,u){"use strict";J();function o(l,v){let b=new SyntaxError(l+" ("+v.start.line+":"+v.start.column+")");return b.loc=v,b}u.exports=o}}),ka=Q({"src/utils/try-combinations.js"(a,u){"use strict";J();function o(){let l;for(var v=arguments.length,b=new Array(v),y=0;y<v;y++)b[y]=arguments[y];for(let[I,T]of b.entries())try{return{result:T()}}catch(x){I===0&&(l=x)}return{error:l}}u.exports=o}}),Fa={};_h(Fa,{EOL:()=>cr,arch:()=>kh,cpus:()=>Oa,default:()=>qa,endianness:()=>Ba,freemem:()=>Da,getNetworkInterfaces:()=>ja,hostname:()=>Ia,loadavg:()=>Ta,networkInterfaces:()=>Ra,platform:()=>Fh,release:()=>Va,tmpDir:()=>hr,tmpdir:()=>lr,totalmem:()=>Na,type:()=>La,uptime:()=>Pa});function Ba(){if(typeof pt>"u"){var a=new ArrayBuffer(2),u=new Uint8Array(a),o=new Uint16Array(a);if(u[0]=1,u[1]=2,o[0]===258)pt="BE";else if(o[0]===513)pt="LE";else throw new Error("unable to figure out endianess")}return pt}function Ia(){return typeof globalThis.location<"u"?globalThis.location.hostname:""}function Ta(){return[]}function Pa(){return 0}function Da(){return Number.MAX_VALUE}function Na(){return Number.MAX_VALUE}function Oa(){return[]}function La(){return"Browser"}function Va(){return typeof globalThis.navigator<"u"?globalThis.navigator.appVersion:""}function Ra(){}function ja(){}function kh(){return"javascript"}function Fh(){return"browser"}function hr(){return"/tmp"}var pt,lr,cr,qa,Bh=wa({"node-modules-polyfills:os"(){J(),lr=hr,cr=`
`,qa={EOL:cr,tmpdir:lr,tmpDir:hr,networkInterfaces:Ra,getNetworkInterfaces:ja,release:Va,type:La,cpus:Oa,totalmem:Na,freemem:Da,uptime:Pa,loadavg:Ta,hostname:Ia,endianness:Ba}}}),Ih=Q({"node-modules-polyfills-commonjs:os"(a,u){J();var o=(Bh(),wh(Fa));if(o&&o.default){u.exports=o.default;for(let l in o)u.exports[l]=o[l]}else o&&(u.exports=o)}}),Th=Q({"node_modules/detect-newline/index.js"(a,u){"use strict";J();var o=l=>{if(typeof l!="string")throw new TypeError("Expected a string");let v=l.match(/(?:\r?\n)/g)||[];if(v.length===0)return;let b=v.filter(I=>I===`\r
`).length,y=v.length-b;return b>y?`\r
`:`
`};u.exports=o,u.exports.graceful=l=>typeof l=="string"&&o(l)||`
`}}),Ph=Q({"node_modules/jest-docblock/build/index.js"(a){"use strict";J(),Object.defineProperty(a,"__esModule",{value:!0}),a.extract=g,a.parse=G,a.parseWithComments=f,a.print=B,a.strip=w;function u(){let k=Ih();return u=function(){return k},k}function o(){let k=l(Th());return o=function(){return k},k}function l(k){return k&&k.__esModule?k:{default:k}}var v=/\*\/$/,b=/^\/\*\*?/,y=/^\s*(\/\*\*?(.|\r?\n)*?\*\/)/,I=/(^|\s+)\/\/([^\r\n]*)/g,T=/^(\r?\n)+/,x=/(?:^|\r?\n) *(@[^\r\n]*?) *\r?\n *(?![^@\r\n]*\/\/[^]*)([^@\r\n\s][^@\r\n]+?) *\r?\n/g,R=/(?:^|\r?\n) *@(\S+) *([^\r\n]*)/g,U=/(\r?\n|^) *\* ?/g,D=[];function g(k){let X=k.match(y);return X?X[0].trimLeft():""}function w(k){let X=k.match(y);return X&&X[0]?k.substring(X[0].length):k}function G(k){return f(k).pragmas}function f(k){let X=(0,o().default)(k)||u().EOL;k=k.replace(b,"").replace(v,"").replace(U,"$1");let O="";for(;O!==k;)O=k,k=k.replace(x,`${X}$1 $2${X}`);k=k.replace(T,"").trimRight();let i=Object.create(null),S=k.replace(R,"").replace(T,"").trimRight(),F;for(;F=R.exec(k);){let j=F[2].replace(I,"");typeof i[F[1]]=="string"||Array.isArray(i[F[1]])?i[F[1]]=D.concat(i[F[1]],j):i[F[1]]=j}return{comments:S,pragmas:i}}function B(k){let{comments:X="",pragmas:O={}}=k,i=(0,o().default)(X)||u().EOL,S="/**",F=" *",j=" */",Z=Object.keys(O),ne=Z.map(ie=>V(ie,O[ie])).reduce((ie,Ne)=>ie.concat(Ne),[]).map(ie=>`${F} ${ie}${i}`).join("");if(!X){if(Z.length===0)return"";if(Z.length===1&&!Array.isArray(O[Z[0]])){let ie=O[Z[0]];return`${S} ${V(Z[0],ie)[0]}${j}`}}let ee=X.split(i).map(ie=>`${F} ${ie}`).join(i)+i;return S+i+(X?ee:"")+(X&&Z.length?F+i:"")+ne+j}function V(k,X){return D.concat(X).map(O=>`@${k} ${O}`.trim())}}}),Dh=Q({"src/common/end-of-line.js"(a,u){"use strict";J();function o(y){let I=y.indexOf("\r");return I>=0?y.charAt(I+1)===`
`?"crlf":"cr":"lf"}function l(y){switch(y){case"cr":return"\r";case"crlf":return`\r
`;default:return`
`}}function v(y,I){let T;switch(I){case`
`:T=/\n/g;break;case"\r":T=/\r/g;break;case`\r
`:T=/\r\n/g;break;default:throw new Error(`Unexpected "eol" ${JSON.stringify(I)}.`)}let x=y.match(T);return x?x.length:0}function b(y){return y.replace(/\r\n?/g,`
`)}u.exports={guessEndOfLine:o,convertEndOfLineToChars:l,countEndOfLineChars:v,normalizeEndOfLine:b}}}),Nh=Q({"src/language-js/utils/get-shebang.js"(a,u){"use strict";J();function o(l){if(!l.startsWith("#!"))return"";let v=l.indexOf(`
`);return v===-1?l:l.slice(0,v)}u.exports=o}}),Oh=Q({"src/language-js/pragma.js"(a,u){"use strict";J();var{parseWithComments:o,strip:l,extract:v,print:b}=Ph(),{normalizeEndOfLine:y}=Dh(),I=Nh();function T(U){let D=I(U);D&&(U=U.slice(D.length+1));let g=v(U),{pragmas:w,comments:G}=o(g);return{shebang:D,text:U,pragmas:w,comments:G}}function x(U){let D=Object.keys(T(U).pragmas);return D.includes("prettier")||D.includes("format")}function R(U){let{shebang:D,text:g,pragmas:w,comments:G}=T(U),f=l(g),B=b({pragmas:Object.assign({format:""},w),comments:G.trimStart()});return(D?`${D}
`:"")+y(B)+(f.startsWith(`
`)?`
`:`

`)+f}u.exports={hasPragma:x,insertPragma:R}}}),Lh=Q({"src/utils/is-non-empty-array.js"(a,u){"use strict";J();function o(l){return Array.isArray(l)&&l.length>0}u.exports=o}}),Ma=Q({"src/language-js/loc.js"(a,u){"use strict";J();var o=Lh();function l(T){var x,R;let U=T.range?T.range[0]:T.start,D=(x=(R=T.declaration)===null||R===void 0?void 0:R.decorators)!==null&&x!==void 0?x:T.decorators;return o(D)?Math.min(l(D[0]),U):U}function v(T){return T.range?T.range[1]:T.end}function b(T,x){let R=l(T);return Number.isInteger(R)&&R===l(x)}function y(T,x){let R=v(T);return Number.isInteger(R)&&R===v(x)}function I(T,x){return b(T,x)&&y(T,x)}u.exports={locStart:l,locEnd:v,hasSameLocStart:b,hasSameLoc:I}}}),Ua=Q({"src/language-js/parse/utils/create-parser.js"(a,u){"use strict";J();var{hasPragma:o}=Oh(),{locStart:l,locEnd:v}=Ma();function b(y){return y=typeof y=="function"?{parse:y}:y,Object.assign({astFormat:"estree",hasPragma:o,locStart:l,locEnd:v},y)}u.exports=b}}),Vh=Q({"src/language-js/utils/is-ts-keyword-type.js"(a,u){"use strict";J();function o(l){let{type:v}=l;return v.startsWith("TS")&&v.endsWith("Keyword")}u.exports=o}}),Rh=Q({"src/language-js/utils/is-block-comment.js"(a,u){"use strict";J();var o=new Set(["Block","CommentBlock","MultiLine"]),l=v=>o.has(v==null?void 0:v.type);u.exports=l}}),jh=Q({"src/language-js/utils/is-type-cast-comment.js"(a,u){"use strict";J();var o=Rh();function l(v){return o(v)&&v.value[0]==="*"&&/@(?:type|satisfies)\b/.test(v.value)}u.exports=l}}),qh=Q({"src/utils/get-last.js"(a,u){"use strict";J();var o=l=>l[l.length-1];u.exports=o}}),Mh=Q({"src/language-js/parse/postprocess/visit-node.js"(a,u){"use strict";J();function o(l,v){if(Array.isArray(l)){for(let b=0;b<l.length;b++)l[b]=o(l[b],v);return l}if(l&&typeof l=="object"&&typeof l.type=="string"){let b=Object.keys(l);for(let y=0;y<b.length;y++)l[b[y]]=o(l[b[y]],v);return v(l)||l}return l}u.exports=o}}),Uh=Q({"src/language-js/parse/postprocess/throw-syntax-error.js"(a,u){"use strict";J();var o=dr();function l(v,b){let{start:y,end:I}=v.loc;throw o(b,{start:{line:y.line,column:y.column+1},end:{line:I.line,column:I.column+1}})}u.exports=l}}),Wa=Q({"src/language-js/parse/postprocess/index.js"(a,u){"use strict";J();var{locStart:o,locEnd:l}=Ma(),v=Vh(),b=jh(),y=qh(),I=Mh(),T=Uh();function x(g,w){if(w.parser!=="typescript"&&w.parser!=="flow"&&w.parser!=="acorn"&&w.parser!=="espree"&&w.parser!=="meriyah"){let f=new Set;g=I(g,B=>{B.leadingComments&&B.leadingComments.some(b)&&f.add(o(B))}),g=I(g,B=>{if(B.type==="ParenthesizedExpression"){let{expression:V}=B;if(V.type==="TypeCastExpression")return V.range=B.range,V;let k=o(B);if(!f.has(k))return V.extra=Object.assign(Object.assign({},V.extra),{},{parenthesized:!0}),V}})}return g=I(g,f=>{switch(f.type){case"ChainExpression":return R(f.expression);case"LogicalExpression":{if(U(f))return D(f);break}case"VariableDeclaration":{let B=y(f.declarations);B&&B.init&&G(f,B);break}case"TSParenthesizedType":return v(f.typeAnnotation)||f.typeAnnotation.type==="TSThisType"||(f.typeAnnotation.range=[o(f),l(f)]),f.typeAnnotation;case"TSTypeParameter":if(typeof f.name=="string"){let B=o(f);f.name={type:"Identifier",name:f.name,range:[B,B+f.name.length]}}break;case"ObjectExpression":if(w.parser==="typescript"){let B=f.properties.find(V=>V.type==="Property"&&V.value.type==="TSEmptyBodyFunctionExpression");B&&T(B.value,"Unexpected token.")}break;case"SequenceExpression":{let B=y(f.expressions);f.range=[o(f),Math.min(l(B),l(f))];break}case"TopicReference":w.__isUsingHackPipeline=!0;break;case"ExportAllDeclaration":{let{exported:B}=f;if(w.parser==="meriyah"&&B&&B.type==="Identifier"){let V=w.originalText.slice(o(B),l(B));(V.startsWith('"')||V.startsWith("'"))&&(f.exported=Object.assign(Object.assign({},f.exported),{},{type:"Literal",value:f.exported.name,raw:V}))}break}case"PropertyDefinition":if(w.parser==="meriyah"&&f.static&&!f.computed&&!f.key){let B="static",V=o(f);Object.assign(f,{static:!1,key:{type:"Identifier",name:B,range:[V,V+B.length]}})}break}}),g;function G(f,B){w.originalText[l(B)]!==";"&&(f.range=[o(f),l(B)])}}function R(g){switch(g.type){case"CallExpression":g.type="OptionalCallExpression",g.callee=R(g.callee);break;case"MemberExpression":g.type="OptionalMemberExpression",g.object=R(g.object);break;case"TSNonNullExpression":g.expression=R(g.expression);break}return g}function U(g){return g.type==="LogicalExpression"&&g.right.type==="LogicalExpression"&&g.operator===g.right.operator}function D(g){return U(g)?D({type:"LogicalExpression",operator:g.operator,left:D({type:"LogicalExpression",operator:g.operator,left:g.left,right:g.right.left,range:[o(g.left),l(g.right.left)]}),right:g.right.right,range:[o(g),l(g)]}):g}u.exports=x}}),ft=Q({"node_modules/acorn/dist/acorn.js"(a,u){J(),function(o,l){typeof a=="object"&&typeof u<"u"?l(a):typeof define=="function"&&define.amd?define(["exports"],l):(o=typeof globalThis<"u"?globalThis:o||self,l(o.acorn={}))}(a,function(o){"use strict";var l=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,154,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,87,9,39,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,4706,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,262,6,10,9,357,0,62,13,1495,6,110,6,6,9,4759,9,787719,239],v=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,68,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,190,0,80,921,103,110,18,195,2637,96,16,1070,4050,582,8634,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8936,3,2,6,2,1,2,290,46,2,18,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,482,44,11,6,17,0,322,29,19,43,1269,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4152,8,221,3,5761,15,7472,3104,541,1507,4938],b="\u200C\u200D\xB7\u0300-\u036F\u0387\u0483-\u0487\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u0669\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u06F0-\u06F9\u0711\u0730-\u074A\u07A6-\u07B0\u07C0-\u07C9\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0898-\u089F\u08CA-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0966-\u096F\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09E6-\u09EF\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A66-\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AE6-\u0AEF\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B62\u0B63\u0B66-\u0B6F\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0BE6-\u0BEF\u0C00-\u0C04\u0C3C\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0CE6-\u0CEF\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D66-\u0D6F\u0D81-\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0E50-\u0E59\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECD\u0ED0-\u0ED9\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1040-\u1049\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F-\u109D\u135D-\u135F\u1369-\u1371\u1712-\u1715\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u17E0-\u17E9\u180B-\u180D\u180F-\u1819\u18A9\u1920-\u192B\u1930-\u193B\u1946-\u194F\u19D0-\u19DA\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AB0-\u1ABD\u1ABF-\u1ACE\u1B00-\u1B04\u1B34-\u1B44\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BB0-\u1BB9\u1BE6-\u1BF3\u1C24-\u1C37\u1C40-\u1C49\u1C50-\u1C59\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DFF\u203F\u2040\u2054\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA620-\uA629\uA66F\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA82C\uA880\uA881\uA8B4-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F1\uA8FF-\uA909\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9D0-\uA9D9\uA9E5\uA9F0-\uA9F9\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA50-\uAA59\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uABF0-\uABF9\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFF10-\uFF19\uFF3F",y="\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC",I={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"},T="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this",x={5:T,"5module":T+" export import",6:T+" const class extends export import super"},R=/^in(stanceof)?$/,U=new RegExp("["+y+"]"),D=new RegExp("["+y+b+"]");function g(e,t){for(var r=65536,s=0;s<t.length;s+=2){if(r+=t[s],r>e)return!1;if(r+=t[s+1],r>=e)return!0}}function w(e,t){return e<65?e===36:e<91?!0:e<97?e===95:e<123?!0:e<=65535?e>=170&&U.test(String.fromCharCode(e)):t===!1?!1:g(e,v)}function G(e,t){return e<48?e===36:e<58?!0:e<65?!1:e<91?!0:e<97?e===95:e<123?!0:e<=65535?e>=170&&D.test(String.fromCharCode(e)):t===!1?!1:g(e,v)||g(e,l)}var f=function(t,r){r===void 0&&(r={}),this.label=t,this.keyword=r.keyword,this.beforeExpr=!!r.beforeExpr,this.startsExpr=!!r.startsExpr,this.isLoop=!!r.isLoop,this.isAssign=!!r.isAssign,this.prefix=!!r.prefix,this.postfix=!!r.postfix,this.binop=r.binop||null,this.updateContext=null};function B(e,t){return new f(e,{beforeExpr:!0,binop:t})}var V={beforeExpr:!0},k={startsExpr:!0},X={};function O(e,t){return t===void 0&&(t={}),t.keyword=e,X[e]=new f(e,t)}var i={num:new f("num",k),regexp:new f("regexp",k),string:new f("string",k),name:new f("name",k),privateId:new f("privateId",k),eof:new f("eof"),bracketL:new f("[",{beforeExpr:!0,startsExpr:!0}),bracketR:new f("]"),braceL:new f("{",{beforeExpr:!0,startsExpr:!0}),braceR:new f("}"),parenL:new f("(",{beforeExpr:!0,startsExpr:!0}),parenR:new f(")"),comma:new f(",",V),semi:new f(";",V),colon:new f(":",V),dot:new f("."),question:new f("?",V),questionDot:new f("?."),arrow:new f("=>",V),template:new f("template"),invalidTemplate:new f("invalidTemplate"),ellipsis:new f("...",V),backQuote:new f("`",k),dollarBraceL:new f("${",{beforeExpr:!0,startsExpr:!0}),eq:new f("=",{beforeExpr:!0,isAssign:!0}),assign:new f("_=",{beforeExpr:!0,isAssign:!0}),incDec:new f("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),prefix:new f("!/~",{beforeExpr:!0,prefix:!0,startsExpr:!0}),logicalOR:B("||",1),logicalAND:B("&&",2),bitwiseOR:B("|",3),bitwiseXOR:B("^",4),bitwiseAND:B("&",5),equality:B("==/!=/===/!==",6),relational:B("</>/<=/>=",7),bitShift:B("<</>>/>>>",8),plusMin:new f("+/-",{beforeExpr:!0,binop:9,prefix:!0,startsExpr:!0}),modulo:B("%",10),star:B("*",10),slash:B("/",10),starstar:new f("**",{beforeExpr:!0}),coalesce:B("??",1),_break:O("break"),_case:O("case",V),_catch:O("catch"),_continue:O("continue"),_debugger:O("debugger"),_default:O("default",V),_do:O("do",{isLoop:!0,beforeExpr:!0}),_else:O("else",V),_finally:O("finally"),_for:O("for",{isLoop:!0}),_function:O("function",k),_if:O("if"),_return:O("return",V),_switch:O("switch"),_throw:O("throw",V),_try:O("try"),_var:O("var"),_const:O("const"),_while:O("while",{isLoop:!0}),_with:O("with"),_new:O("new",{beforeExpr:!0,startsExpr:!0}),_this:O("this",k),_super:O("super",k),_class:O("class",k),_extends:O("extends",V),_export:O("export"),_import:O("import",k),_null:O("null",k),_true:O("true",k),_false:O("false",k),_in:O("in",{beforeExpr:!0,binop:7}),_instanceof:O("instanceof",{beforeExpr:!0,binop:7}),_typeof:O("typeof",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_void:O("void",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_delete:O("delete",{beforeExpr:!0,prefix:!0,startsExpr:!0})},S=/\r\n?|\n|\u2028|\u2029/,F=new RegExp(S.source,"g");function j(e){return e===10||e===13||e===8232||e===8233}function Z(e,t,r){r===void 0&&(r=e.length);for(var s=t;s<r;s++){var n=e.charCodeAt(s);if(j(n))return s<r-1&&n===13&&e.charCodeAt(s+1)===10?s+2:s+1}return-1}var ne=/[\u1680\u2000-\u200a\u202f\u205f\u3000\ufeff]/,ee=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,ie=Object.prototype,Ne=ie.hasOwnProperty,p=ie.toString,P=Object.hasOwn||function(e,t){return Ne.call(e,t)},_=Array.isArray||function(e){return p.call(e)==="[object Array]"};function d(e){return new RegExp("^(?:"+e.replace(/ /g,"|")+")$")}function C(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode((e>>10)+55296,(e&1023)+56320))}var K=/(?:[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/,H=function(t,r){this.line=t,this.column=r};H.prototype.offset=function(t){return new H(this.line,this.column+t)};var te=function(t,r,s){this.start=r,this.end=s,t.sourceFile!==null&&(this.source=t.sourceFile)};function ae(e,t){for(var r=1,s=0;;){var n=Z(e,s,t);if(n<0)return new H(r,t-s);++r,s=n}}var fe={ecmaVersion:null,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowAwaitOutsideFunction:null,allowSuperOutsideMethod:null,allowHashBang:!1,locations:!1,onToken:null,onComment:null,ranges:!1,program:null,sourceFile:null,directSourceFile:null,preserveParens:!1},Ae=!1;function dt(e){var t={};for(var r in fe)t[r]=e&&P(e,r)?e[r]:fe[r];if(t.ecmaVersion==="latest"?t.ecmaVersion=1e8:t.ecmaVersion==null?(!Ae&&typeof console=="object"&&console.warn&&(Ae=!0,console.warn(`Since Acorn 8.0.0, options.ecmaVersion is required.
Defaulting to 2020, but this will stop working in the future.`)),t.ecmaVersion=11):t.ecmaVersion>=2015&&(t.ecmaVersion-=2009),t.allowReserved==null&&(t.allowReserved=t.ecmaVersion<5),e.allowHashBang==null&&(t.allowHashBang=t.ecmaVersion>=14),_(t.onToken)){var s=t.onToken;t.onToken=function(n){return s.push(n)}}return _(t.onComment)&&(t.onComment=mt(t,t.onComment)),t}function mt(e,t){return function(r,s,n,h,c,m){var A={type:r?"Block":"Line",value:s,start:n,end:h};e.locations&&(A.loc=new te(this,c,m)),e.ranges&&(A.range=[n,h]),t.push(A)}}var _e=1,Ce=2,Oe=4,ze=8,mr=16,vr=32,vt=64,gr=128,Le=256,gt=_e|Ce|Le;function xt(e,t){return Ce|(e?Oe:0)|(t?ze:0)}var Ge=0,yt=1,ve=2,xr=3,yr=4,Ar=5,Y=function(t,r,s){this.options=t=dt(t),this.sourceFile=t.sourceFile,this.keywords=d(x[t.ecmaVersion>=6?6:t.sourceType==="module"?"5module":5]);var n="";t.allowReserved!==!0&&(n=I[t.ecmaVersion>=6?6:t.ecmaVersion===5?5:3],t.sourceType==="module"&&(n+=" await")),this.reservedWords=d(n);var h=(n?n+" ":"")+I.strict;this.reservedWordsStrict=d(h),this.reservedWordsStrictBind=d(h+" "+I.strictBind),this.input=String(r),this.containsEsc=!1,s?(this.pos=s,this.lineStart=this.input.lastIndexOf(`
`,s-1)+1,this.curLine=this.input.slice(0,this.lineStart).split(S).length):(this.pos=this.lineStart=0,this.curLine=1),this.type=i.eof,this.value=null,this.start=this.end=this.pos,this.startLoc=this.endLoc=this.curPosition(),this.lastTokEndLoc=this.lastTokStartLoc=null,this.lastTokStart=this.lastTokEnd=this.pos,this.context=this.initialContext(),this.exprAllowed=!0,this.inModule=t.sourceType==="module",this.strict=this.inModule||this.strictDirective(this.pos),this.potentialArrowAt=-1,this.potentialArrowInForAwait=!1,this.yieldPos=this.awaitPos=this.awaitIdentPos=0,this.labels=[],this.undefinedExports=Object.create(null),this.pos===0&&t.allowHashBang&&this.input.slice(0,2)==="#!"&&this.skipLineComment(2),this.scopeStack=[],this.enterScope(_e),this.regexpState=null,this.privateNameStack=[]},de={inFunction:{configurable:!0},inGenerator:{configurable:!0},inAsync:{configurable:!0},canAwait:{configurable:!0},allowSuper:{configurable:!0},allowDirectSuper:{configurable:!0},treatFunctionsAsVar:{configurable:!0},allowNewDotTarget:{configurable:!0},inClassStaticBlock:{configurable:!0}};Y.prototype.parse=function(){var t=this.options.program||this.startNode();return this.nextToken(),this.parseTopLevel(t)},de.inFunction.get=function(){return(this.currentVarScope().flags&Ce)>0},de.inGenerator.get=function(){return(this.currentVarScope().flags&ze)>0&&!this.currentVarScope().inClassFieldInit},de.inAsync.get=function(){return(this.currentVarScope().flags&Oe)>0&&!this.currentVarScope().inClassFieldInit},de.canAwait.get=function(){for(var e=this.scopeStack.length-1;e>=0;e--){var t=this.scopeStack[e];if(t.inClassFieldInit||t.flags&Le)return!1;if(t.flags&Ce)return(t.flags&Oe)>0}return this.inModule&&this.options.ecmaVersion>=13||this.options.allowAwaitOutsideFunction},de.allowSuper.get=function(){var e=this.currentThisScope(),t=e.flags,r=e.inClassFieldInit;return(t&vt)>0||r||this.options.allowSuperOutsideMethod},de.allowDirectSuper.get=function(){return(this.currentThisScope().flags&gr)>0},de.treatFunctionsAsVar.get=function(){return this.treatFunctionsAsVarInScope(this.currentScope())},de.allowNewDotTarget.get=function(){var e=this.currentThisScope(),t=e.flags,r=e.inClassFieldInit;return(t&(Ce|Le))>0||r},de.inClassStaticBlock.get=function(){return(this.currentVarScope().flags&Le)>0},Y.extend=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];for(var s=this,n=0;n<t.length;n++)s=t[n](s);return s},Y.parse=function(t,r){return new this(r,t).parse()},Y.parseExpressionAt=function(t,r,s){var n=new this(s,t,r);return n.nextToken(),n.parseExpression()},Y.tokenizer=function(t,r){return new this(r,t)},Object.defineProperties(Y.prototype,de);var se=Y.prototype,Ga=/^(?:'((?:\\.|[^'\\])*?)'|"((?:\\.|[^"\\])*?)")/;se.strictDirective=function(e){if(this.options.ecmaVersion<5)return!1;for(;;){ee.lastIndex=e,e+=ee.exec(this.input)[0].length;var t=Ga.exec(this.input.slice(e));if(!t)return!1;if((t[1]||t[2])==="use strict"){ee.lastIndex=e+t[0].length;var r=ee.exec(this.input),s=r.index+r[0].length,n=this.input.charAt(s);return n===";"||n==="}"||S.test(r[0])&&!(/[(`.[+\-/*%<>=,?^&]/.test(n)||n==="!"&&this.input.charAt(s+1)==="=")}e+=t[0].length,ee.lastIndex=e,e+=ee.exec(this.input)[0].length,this.input[e]===";"&&e++}},se.eat=function(e){return this.type===e?(this.next(),!0):!1},se.isContextual=function(e){return this.type===i.name&&this.value===e&&!this.containsEsc},se.eatContextual=function(e){return this.isContextual(e)?(this.next(),!0):!1},se.expectContextual=function(e){this.eatContextual(e)||this.unexpected()},se.canInsertSemicolon=function(){return this.type===i.eof||this.type===i.braceR||S.test(this.input.slice(this.lastTokEnd,this.start))},se.insertSemicolon=function(){if(this.canInsertSemicolon())return this.options.onInsertedSemicolon&&this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc),!0},se.semicolon=function(){!this.eat(i.semi)&&!this.insertSemicolon()&&this.unexpected()},se.afterTrailingComma=function(e,t){if(this.type===e)return this.options.onTrailingComma&&this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc),t||this.next(),!0},se.expect=function(e){this.eat(e)||this.unexpected()},se.unexpected=function(e){this.raise(e!=null?e:this.start,"Unexpected token")};var He=function(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=this.doubleProto=-1};se.checkPatternErrors=function(e,t){if(e){e.trailingComma>-1&&this.raiseRecoverable(e.trailingComma,"Comma is not permitted after the rest element");var r=t?e.parenthesizedAssign:e.parenthesizedBind;r>-1&&this.raiseRecoverable(r,t?"Assigning to rvalue":"Parenthesized pattern")}},se.checkExpressionErrors=function(e,t){if(!e)return!1;var r=e.shorthandAssign,s=e.doubleProto;if(!t)return r>=0||s>=0;r>=0&&this.raise(r,"Shorthand property assignments are valid only in destructuring patterns"),s>=0&&this.raiseRecoverable(s,"Redefinition of __proto__ property")},se.checkYieldAwaitInDefaultParams=function(){this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)&&this.raise(this.yieldPos,"Yield expression cannot be a default value"),this.awaitPos&&this.raise(this.awaitPos,"Await expression cannot be a default value")},se.isSimpleAssignTarget=function(e){return e.type==="ParenthesizedExpression"?this.isSimpleAssignTarget(e.expression):e.type==="Identifier"||e.type==="MemberExpression"};var L=Y.prototype;L.parseTopLevel=function(e){var t=Object.create(null);for(e.body||(e.body=[]);this.type!==i.eof;){var r=this.parseStatement(null,!0,t);e.body.push(r)}if(this.inModule)for(var s=0,n=Object.keys(this.undefinedExports);s<n.length;s+=1){var h=n[s];this.raiseRecoverable(this.undefinedExports[h].start,"Export '"+h+"' is not defined")}return this.adaptDirectivePrologue(e.body),this.next(),e.sourceType=this.options.sourceType,this.finishNode(e,"Program")};var At={kind:"loop"},Ha={kind:"switch"};L.isLet=function(e){if(this.options.ecmaVersion<6||!this.isContextual("let"))return!1;ee.lastIndex=this.pos;var t=ee.exec(this.input),r=this.pos+t[0].length,s=this.input.charCodeAt(r);if(s===91||s===92||s>55295&&s<56320)return!0;if(e)return!1;if(s===123)return!0;if(w(s,!0)){for(var n=r+1;G(s=this.input.charCodeAt(n),!0);)++n;if(s===92||s>55295&&s<56320)return!0;var h=this.input.slice(r,n);if(!R.test(h))return!0}return!1},L.isAsyncFunction=function(){if(this.options.ecmaVersion<8||!this.isContextual("async"))return!1;ee.lastIndex=this.pos;var e=ee.exec(this.input),t=this.pos+e[0].length,r;return!S.test(this.input.slice(this.pos,t))&&this.input.slice(t,t+8)==="function"&&(t+8===this.input.length||!(G(r=this.input.charCodeAt(t+8))||r>55295&&r<56320))},L.parseStatement=function(e,t,r){var s=this.type,n=this.startNode(),h;switch(this.isLet(e)&&(s=i._var,h="let"),s){case i._break:case i._continue:return this.parseBreakContinueStatement(n,s.keyword);case i._debugger:return this.parseDebuggerStatement(n);case i._do:return this.parseDoStatement(n);case i._for:return this.parseForStatement(n);case i._function:return e&&(this.strict||e!=="if"&&e!=="label")&&this.options.ecmaVersion>=6&&this.unexpected(),this.parseFunctionStatement(n,!1,!e);case i._class:return e&&this.unexpected(),this.parseClass(n,!0);case i._if:return this.parseIfStatement(n);case i._return:return this.parseReturnStatement(n);case i._switch:return this.parseSwitchStatement(n);case i._throw:return this.parseThrowStatement(n);case i._try:return this.parseTryStatement(n);case i._const:case i._var:return h=h||this.value,e&&h!=="var"&&this.unexpected(),this.parseVarStatement(n,h);case i._while:return this.parseWhileStatement(n);case i._with:return this.parseWithStatement(n);case i.braceL:return this.parseBlock(!0,n);case i.semi:return this.parseEmptyStatement(n);case i._export:case i._import:if(this.options.ecmaVersion>10&&s===i._import){ee.lastIndex=this.pos;var c=ee.exec(this.input),m=this.pos+c[0].length,A=this.input.charCodeAt(m);if(A===40||A===46)return this.parseExpressionStatement(n,this.parseExpression())}return this.options.allowImportExportEverywhere||(t||this.raise(this.start,"'import' and 'export' may only appear at the top level"),this.inModule||this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")),s===i._import?this.parseImport(n):this.parseExport(n,r);default:if(this.isAsyncFunction())return e&&this.unexpected(),this.next(),this.parseFunctionStatement(n,!0,!e);var q=this.value,W=this.parseExpression();return s===i.name&&W.type==="Identifier"&&this.eat(i.colon)?this.parseLabeledStatement(n,q,W,e):this.parseExpressionStatement(n,W)}},L.parseBreakContinueStatement=function(e,t){var r=t==="break";this.next(),this.eat(i.semi)||this.insertSemicolon()?e.label=null:this.type!==i.name?this.unexpected():(e.label=this.parseIdent(),this.semicolon());for(var s=0;s<this.labels.length;++s){var n=this.labels[s];if((e.label==null||n.name===e.label.name)&&(n.kind!=null&&(r||n.kind==="loop")||e.label&&r))break}return s===this.labels.length&&this.raise(e.start,"Unsyntactic "+t),this.finishNode(e,r?"BreakStatement":"ContinueStatement")},L.parseDebuggerStatement=function(e){return this.next(),this.semicolon(),this.finishNode(e,"DebuggerStatement")},L.parseDoStatement=function(e){return this.next(),this.labels.push(At),e.body=this.parseStatement("do"),this.labels.pop(),this.expect(i._while),e.test=this.parseParenExpression(),this.options.ecmaVersion>=6?this.eat(i.semi):this.semicolon(),this.finishNode(e,"DoWhileStatement")},L.parseForStatement=function(e){this.next();var t=this.options.ecmaVersion>=9&&this.canAwait&&this.eatContextual("await")?this.lastTokStart:-1;if(this.labels.push(At),this.enterScope(0),this.expect(i.parenL),this.type===i.semi)return t>-1&&this.unexpected(t),this.parseFor(e,null);var r=this.isLet();if(this.type===i._var||this.type===i._const||r){var s=this.startNode(),n=r?"let":this.value;return this.next(),this.parseVar(s,!0,n),this.finishNode(s,"VariableDeclaration"),(this.type===i._in||this.options.ecmaVersion>=6&&this.isContextual("of"))&&s.declarations.length===1?(this.options.ecmaVersion>=9&&(this.type===i._in?t>-1&&this.unexpected(t):e.await=t>-1),this.parseForIn(e,s)):(t>-1&&this.unexpected(t),this.parseFor(e,s))}var h=this.isContextual("let"),c=!1,m=new He,A=this.parseExpression(t>-1?"await":!0,m);return this.type===i._in||(c=this.options.ecmaVersion>=6&&this.isContextual("of"))?(this.options.ecmaVersion>=9&&(this.type===i._in?t>-1&&this.unexpected(t):e.await=t>-1),h&&c&&this.raise(A.start,"The left-hand side of a for-of loop may not start with 'let'."),this.toAssignable(A,!1,m),this.checkLValPattern(A),this.parseForIn(e,A)):(this.checkExpressionErrors(m,!0),t>-1&&this.unexpected(t),this.parseFor(e,A))},L.parseFunctionStatement=function(e,t,r){return this.next(),this.parseFunction(e,Ve|(r?0:Ct),!1,t)},L.parseIfStatement=function(e){return this.next(),e.test=this.parseParenExpression(),e.consequent=this.parseStatement("if"),e.alternate=this.eat(i._else)?this.parseStatement("if"):null,this.finishNode(e,"IfStatement")},L.parseReturnStatement=function(e){return!this.inFunction&&!this.options.allowReturnOutsideFunction&&this.raise(this.start,"'return' outside of function"),this.next(),this.eat(i.semi)||this.insertSemicolon()?e.argument=null:(e.argument=this.parseExpression(),this.semicolon()),this.finishNode(e,"ReturnStatement")},L.parseSwitchStatement=function(e){this.next(),e.discriminant=this.parseParenExpression(),e.cases=[],this.expect(i.braceL),this.labels.push(Ha),this.enterScope(0);for(var t,r=!1;this.type!==i.braceR;)if(this.type===i._case||this.type===i._default){var s=this.type===i._case;t&&this.finishNode(t,"SwitchCase"),e.cases.push(t=this.startNode()),t.consequent=[],this.next(),s?t.test=this.parseExpression():(r&&this.raiseRecoverable(this.lastTokStart,"Multiple default clauses"),r=!0,t.test=null),this.expect(i.colon)}else t||this.unexpected(),t.consequent.push(this.parseStatement(null));return this.exitScope(),t&&this.finishNode(t,"SwitchCase"),this.next(),this.labels.pop(),this.finishNode(e,"SwitchStatement")},L.parseThrowStatement=function(e){return this.next(),S.test(this.input.slice(this.lastTokEnd,this.start))&&this.raise(this.lastTokEnd,"Illegal newline after throw"),e.argument=this.parseExpression(),this.semicolon(),this.finishNode(e,"ThrowStatement")};var Ka=[];L.parseTryStatement=function(e){if(this.next(),e.block=this.parseBlock(),e.handler=null,this.type===i._catch){var t=this.startNode();if(this.next(),this.eat(i.parenL)){t.param=this.parseBindingAtom();var r=t.param.type==="Identifier";this.enterScope(r?vr:0),this.checkLValPattern(t.param,r?yr:ve),this.expect(i.parenR)}else this.options.ecmaVersion<10&&this.unexpected(),t.param=null,this.enterScope(0);t.body=this.parseBlock(!1),this.exitScope(),e.handler=this.finishNode(t,"CatchClause")}return e.finalizer=this.eat(i._finally)?this.parseBlock():null,!e.handler&&!e.finalizer&&this.raise(e.start,"Missing catch or finally clause"),this.finishNode(e,"TryStatement")},L.parseVarStatement=function(e,t){return this.next(),this.parseVar(e,!1,t),this.semicolon(),this.finishNode(e,"VariableDeclaration")},L.parseWhileStatement=function(e){return this.next(),e.test=this.parseParenExpression(),this.labels.push(At),e.body=this.parseStatement("while"),this.labels.pop(),this.finishNode(e,"WhileStatement")},L.parseWithStatement=function(e){return this.strict&&this.raise(this.start,"'with' in strict mode"),this.next(),e.object=this.parseParenExpression(),e.body=this.parseStatement("with"),this.finishNode(e,"WithStatement")},L.parseEmptyStatement=function(e){return this.next(),this.finishNode(e,"EmptyStatement")},L.parseLabeledStatement=function(e,t,r,s){for(var n=0,h=this.labels;n<h.length;n+=1){var c=h[n];c.name===t&&this.raise(r.start,"Label '"+t+"' is already declared")}for(var m=this.type.isLoop?"loop":this.type===i._switch?"switch":null,A=this.labels.length-1;A>=0;A--){var q=this.labels[A];if(q.statementStart===e.start)q.statementStart=this.start,q.kind=m;else break}return this.labels.push({name:t,kind:m,statementStart:this.start}),e.body=this.parseStatement(s?s.indexOf("label")===-1?s+"label":s:"label"),this.labels.pop(),e.label=r,this.finishNode(e,"LabeledStatement")},L.parseExpressionStatement=function(e,t){return e.expression=t,this.semicolon(),this.finishNode(e,"ExpressionStatement")},L.parseBlock=function(e,t,r){for(e===void 0&&(e=!0),t===void 0&&(t=this.startNode()),t.body=[],this.expect(i.braceL),e&&this.enterScope(0);this.type!==i.braceR;){var s=this.parseStatement(null);t.body.push(s)}return r&&(this.strict=!1),this.next(),e&&this.exitScope(),this.finishNode(t,"BlockStatement")},L.parseFor=function(e,t){return e.init=t,this.expect(i.semi),e.test=this.type===i.semi?null:this.parseExpression(),this.expect(i.semi),e.update=this.type===i.parenR?null:this.parseExpression(),this.expect(i.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,"ForStatement")},L.parseForIn=function(e,t){var r=this.type===i._in;return this.next(),t.type==="VariableDeclaration"&&t.declarations[0].init!=null&&(!r||this.options.ecmaVersion<8||this.strict||t.kind!=="var"||t.declarations[0].id.type!=="Identifier")&&this.raise(t.start,(r?"for-in":"for-of")+" loop variable declaration may not have an initializer"),e.left=t,e.right=r?this.parseExpression():this.parseMaybeAssign(),this.expect(i.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,r?"ForInStatement":"ForOfStatement")},L.parseVar=function(e,t,r){for(e.declarations=[],e.kind=r;;){var s=this.startNode();if(this.parseVarId(s,r),this.eat(i.eq)?s.init=this.parseMaybeAssign(t):r==="const"&&!(this.type===i._in||this.options.ecmaVersion>=6&&this.isContextual("of"))?this.unexpected():s.id.type!=="Identifier"&&!(t&&(this.type===i._in||this.isContextual("of")))?this.raise(this.lastTokEnd,"Complex binding patterns require an initialization value"):s.init=null,e.declarations.push(this.finishNode(s,"VariableDeclarator")),!this.eat(i.comma))break}return e},L.parseVarId=function(e,t){e.id=this.parseBindingAtom(),this.checkLValPattern(e.id,t==="var"?yt:ve,!1)};var Ve=1,Ct=2,Cr=4;L.parseFunction=function(e,t,r,s,n){this.initFunction(e),(this.options.ecmaVersion>=9||this.options.ecmaVersion>=6&&!s)&&(this.type===i.star&&t&Ct&&this.unexpected(),e.generator=this.eat(i.star)),this.options.ecmaVersion>=8&&(e.async=!!s),t&Ve&&(e.id=t&Cr&&this.type!==i.name?null:this.parseIdent(),e.id&&!(t&Ct)&&this.checkLValSimple(e.id,this.strict||e.generator||e.async?this.treatFunctionsAsVar?yt:ve:xr));var h=this.yieldPos,c=this.awaitPos,m=this.awaitIdentPos;return this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(xt(e.async,e.generator)),t&Ve||(e.id=this.type===i.name?this.parseIdent():null),this.parseFunctionParams(e),this.parseFunctionBody(e,r,!1,n),this.yieldPos=h,this.awaitPos=c,this.awaitIdentPos=m,this.finishNode(e,t&Ve?"FunctionDeclaration":"FunctionExpression")},L.parseFunctionParams=function(e){this.expect(i.parenL),e.params=this.parseBindingList(i.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams()},L.parseClass=function(e,t){this.next();var r=this.strict;this.strict=!0,this.parseClassId(e,t),this.parseClassSuper(e);var s=this.enterClassBody(),n=this.startNode(),h=!1;for(n.body=[],this.expect(i.braceL);this.type!==i.braceR;){var c=this.parseClassElement(e.superClass!==null);c&&(n.body.push(c),c.type==="MethodDefinition"&&c.kind==="constructor"?(h&&this.raise(c.start,"Duplicate constructor in the same class"),h=!0):c.key&&c.key.type==="PrivateIdentifier"&&Xa(s,c)&&this.raiseRecoverable(c.key.start,"Identifier '#"+c.key.name+"' has already been declared"))}return this.strict=r,this.next(),e.body=this.finishNode(n,"ClassBody"),this.exitClassBody(),this.finishNode(e,t?"ClassDeclaration":"ClassExpression")},L.parseClassElement=function(e){if(this.eat(i.semi))return null;var t=this.options.ecmaVersion,r=this.startNode(),s="",n=!1,h=!1,c="method",m=!1;if(this.eatContextual("static")){if(t>=13&&this.eat(i.braceL))return this.parseClassStaticBlock(r),r;this.isClassElementNameStart()||this.type===i.star?m=!0:s="static"}if(r.static=m,!s&&t>=8&&this.eatContextual("async")&&((this.isClassElementNameStart()||this.type===i.star)&&!this.canInsertSemicolon()?h=!0:s="async"),!s&&(t>=9||!h)&&this.eat(i.star)&&(n=!0),!s&&!h&&!n){var A=this.value;(this.eatContextual("get")||this.eatContextual("set"))&&(this.isClassElementNameStart()?c=A:s=A)}if(s?(r.computed=!1,r.key=this.startNodeAt(this.lastTokStart,this.lastTokStartLoc),r.key.name=s,this.finishNode(r.key,"Identifier")):this.parseClassElementName(r),t<13||this.type===i.parenL||c!=="method"||n||h){var q=!r.static&&Ke(r,"constructor"),W=q&&e;q&&c!=="method"&&this.raise(r.key.start,"Constructor can't have get/set modifier"),r.kind=q?"constructor":c,this.parseClassMethod(r,n,h,W)}else this.parseClassField(r);return r},L.isClassElementNameStart=function(){return this.type===i.name||this.type===i.privateId||this.type===i.num||this.type===i.string||this.type===i.bracketL||this.type.keyword},L.parseClassElementName=function(e){this.type===i.privateId?(this.value==="constructor"&&this.raise(this.start,"Classes can't have an element named '#constructor'"),e.computed=!1,e.key=this.parsePrivateIdent()):this.parsePropertyName(e)},L.parseClassMethod=function(e,t,r,s){var n=e.key;e.kind==="constructor"?(t&&this.raise(n.start,"Constructor can't be a generator"),r&&this.raise(n.start,"Constructor can't be an async method")):e.static&&Ke(e,"prototype")&&this.raise(n.start,"Classes may not have a static property named prototype");var h=e.value=this.parseMethod(t,r,s);return e.kind==="get"&&h.params.length!==0&&this.raiseRecoverable(h.start,"getter should have no params"),e.kind==="set"&&h.params.length!==1&&this.raiseRecoverable(h.start,"setter should have exactly one param"),e.kind==="set"&&h.params[0].type==="RestElement"&&this.raiseRecoverable(h.params[0].start,"Setter cannot use rest params"),this.finishNode(e,"MethodDefinition")},L.parseClassField=function(e){if(Ke(e,"constructor")?this.raise(e.key.start,"Classes can't have a field named 'constructor'"):e.static&&Ke(e,"prototype")&&this.raise(e.key.start,"Classes can't have a static field named 'prototype'"),this.eat(i.eq)){var t=this.currentThisScope(),r=t.inClassFieldInit;t.inClassFieldInit=!0,e.value=this.parseMaybeAssign(),t.inClassFieldInit=r}else e.value=null;return this.semicolon(),this.finishNode(e,"PropertyDefinition")},L.parseClassStaticBlock=function(e){e.body=[];var t=this.labels;for(this.labels=[],this.enterScope(Le|vt);this.type!==i.braceR;){var r=this.parseStatement(null);e.body.push(r)}return this.next(),this.exitScope(),this.labels=t,this.finishNode(e,"StaticBlock")},L.parseClassId=function(e,t){this.type===i.name?(e.id=this.parseIdent(),t&&this.checkLValSimple(e.id,ve,!1)):(t===!0&&this.unexpected(),e.id=null)},L.parseClassSuper=function(e){e.superClass=this.eat(i._extends)?this.parseExprSubscripts(!1):null},L.enterClassBody=function(){var e={declared:Object.create(null),used:[]};return this.privateNameStack.push(e),e.declared},L.exitClassBody=function(){for(var e=this.privateNameStack.pop(),t=e.declared,r=e.used,s=this.privateNameStack.length,n=s===0?null:this.privateNameStack[s-1],h=0;h<r.length;++h){var c=r[h];P(t,c.name)||(n?n.used.push(c):this.raiseRecoverable(c.start,"Private field '#"+c.name+"' must be declared in an enclosing class"))}};function Xa(e,t){var r=t.key.name,s=e[r],n="true";return t.type==="MethodDefinition"&&(t.kind==="get"||t.kind==="set")&&(n=(t.static?"s":"i")+t.kind),s==="iget"&&n==="iset"||s==="iset"&&n==="iget"||s==="sget"&&n==="sset"||s==="sset"&&n==="sget"?(e[r]="true",!1):s?!0:(e[r]=n,!1)}function Ke(e,t){var r=e.computed,s=e.key;return!r&&(s.type==="Identifier"&&s.name===t||s.type==="Literal"&&s.value===t)}L.parseExport=function(e,t){if(this.next(),this.eat(i.star))return this.options.ecmaVersion>=11&&(this.eatContextual("as")?(e.exported=this.parseModuleExportName(),this.checkExport(t,e.exported,this.lastTokStart)):e.exported=null),this.expectContextual("from"),this.type!==i.string&&this.unexpected(),e.source=this.parseExprAtom(),this.semicolon(),this.finishNode(e,"ExportAllDeclaration");if(this.eat(i._default)){this.checkExport(t,"default",this.lastTokStart);var r;if(this.type===i._function||(r=this.isAsyncFunction())){var s=this.startNode();this.next(),r&&this.next(),e.declaration=this.parseFunction(s,Ve|Cr,!1,r)}else if(this.type===i._class){var n=this.startNode();e.declaration=this.parseClass(n,"nullableID")}else e.declaration=this.parseMaybeAssign(),this.semicolon();return this.finishNode(e,"ExportDefaultDeclaration")}if(this.shouldParseExportStatement())e.declaration=this.parseStatement(null),e.declaration.type==="VariableDeclaration"?this.checkVariableExport(t,e.declaration.declarations):this.checkExport(t,e.declaration.id,e.declaration.id.start),e.specifiers=[],e.source=null;else{if(e.declaration=null,e.specifiers=this.parseExportSpecifiers(t),this.eatContextual("from"))this.type!==i.string&&this.unexpected(),e.source=this.parseExprAtom();else{for(var h=0,c=e.specifiers;h<c.length;h+=1){var m=c[h];this.checkUnreserved(m.local),this.checkLocalExport(m.local),m.local.type==="Literal"&&this.raise(m.local.start,"A string literal cannot be used as an exported binding without `from`.")}e.source=null}this.semicolon()}return this.finishNode(e,"ExportNamedDeclaration")},L.checkExport=function(e,t,r){e&&(typeof t!="string"&&(t=t.type==="Identifier"?t.name:t.value),P(e,t)&&this.raiseRecoverable(r,"Duplicate export '"+t+"'"),e[t]=!0)},L.checkPatternExport=function(e,t){var r=t.type;if(r==="Identifier")this.checkExport(e,t,t.start);else if(r==="ObjectPattern")for(var s=0,n=t.properties;s<n.length;s+=1){var h=n[s];this.checkPatternExport(e,h)}else if(r==="ArrayPattern")for(var c=0,m=t.elements;c<m.length;c+=1){var A=m[c];A&&this.checkPatternExport(e,A)}else r==="Property"?this.checkPatternExport(e,t.value):r==="AssignmentPattern"?this.checkPatternExport(e,t.left):r==="RestElement"?this.checkPatternExport(e,t.argument):r==="ParenthesizedExpression"&&this.checkPatternExport(e,t.expression)},L.checkVariableExport=function(e,t){if(e)for(var r=0,s=t;r<s.length;r+=1){var n=s[r];this.checkPatternExport(e,n.id)}},L.shouldParseExportStatement=function(){return this.type.keyword==="var"||this.type.keyword==="const"||this.type.keyword==="class"||this.type.keyword==="function"||this.isLet()||this.isAsyncFunction()},L.parseExportSpecifiers=function(e){var t=[],r=!0;for(this.expect(i.braceL);!this.eat(i.braceR);){if(r)r=!1;else if(this.expect(i.comma),this.afterTrailingComma(i.braceR))break;var s=this.startNode();s.local=this.parseModuleExportName(),s.exported=this.eatContextual("as")?this.parseModuleExportName():s.local,this.checkExport(e,s.exported,s.exported.start),t.push(this.finishNode(s,"ExportSpecifier"))}return t},L.parseImport=function(e){return this.next(),this.type===i.string?(e.specifiers=Ka,e.source=this.parseExprAtom()):(e.specifiers=this.parseImportSpecifiers(),this.expectContextual("from"),e.source=this.type===i.string?this.parseExprAtom():this.unexpected()),this.semicolon(),this.finishNode(e,"ImportDeclaration")},L.parseImportSpecifiers=function(){var e=[],t=!0;if(this.type===i.name){var r=this.startNode();if(r.local=this.parseIdent(),this.checkLValSimple(r.local,ve),e.push(this.finishNode(r,"ImportDefaultSpecifier")),!this.eat(i.comma))return e}if(this.type===i.star){var s=this.startNode();return this.next(),this.expectContextual("as"),s.local=this.parseIdent(),this.checkLValSimple(s.local,ve),e.push(this.finishNode(s,"ImportNamespaceSpecifier")),e}for(this.expect(i.braceL);!this.eat(i.braceR);){if(t)t=!1;else if(this.expect(i.comma),this.afterTrailingComma(i.braceR))break;var n=this.startNode();n.imported=this.parseModuleExportName(),this.eatContextual("as")?n.local=this.parseIdent():(this.checkUnreserved(n.imported),n.local=n.imported),this.checkLValSimple(n.local,ve),e.push(this.finishNode(n,"ImportSpecifier"))}return e},L.parseModuleExportName=function(){if(this.options.ecmaVersion>=13&&this.type===i.string){var e=this.parseLiteral(this.value);return K.test(e.value)&&this.raise(e.start,"An export name cannot include a lone surrogate."),e}return this.parseIdent(!0)},L.adaptDirectivePrologue=function(e){for(var t=0;t<e.length&&this.isDirectiveCandidate(e[t]);++t)e[t].directive=e[t].expression.raw.slice(1,-1)},L.isDirectiveCandidate=function(e){return this.options.ecmaVersion>=5&&e.type==="ExpressionStatement"&&e.expression.type==="Literal"&&typeof e.expression.value=="string"&&(this.input[e.start]==='"'||this.input[e.start]==="'")};var he=Y.prototype;he.toAssignable=function(e,t,r){if(this.options.ecmaVersion>=6&&e)switch(e.type){case"Identifier":this.inAsync&&e.name==="await"&&this.raise(e.start,"Cannot use 'await' as identifier inside an async function");break;case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":break;case"ObjectExpression":e.type="ObjectPattern",r&&this.checkPatternErrors(r,!0);for(var s=0,n=e.properties;s<n.length;s+=1){var h=n[s];this.toAssignable(h,t),h.type==="RestElement"&&(h.argument.type==="ArrayPattern"||h.argument.type==="ObjectPattern")&&this.raise(h.argument.start,"Unexpected token")}break;case"Property":e.kind!=="init"&&this.raise(e.key.start,"Object pattern can't contain getter or setter"),this.toAssignable(e.value,t);break;case"ArrayExpression":e.type="ArrayPattern",r&&this.checkPatternErrors(r,!0),this.toAssignableList(e.elements,t);break;case"SpreadElement":e.type="RestElement",this.toAssignable(e.argument,t),e.argument.type==="AssignmentPattern"&&this.raise(e.argument.start,"Rest elements cannot have a default value");break;case"AssignmentExpression":e.operator!=="="&&this.raise(e.left.end,"Only '=' operator can be used for specifying default value."),e.type="AssignmentPattern",delete e.operator,this.toAssignable(e.left,t);break;case"ParenthesizedExpression":this.toAssignable(e.expression,t,r);break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":if(!t)break;default:this.raise(e.start,"Assigning to rvalue")}else r&&this.checkPatternErrors(r,!0);return e},he.toAssignableList=function(e,t){for(var r=e.length,s=0;s<r;s++){var n=e[s];n&&this.toAssignable(n,t)}if(r){var h=e[r-1];this.options.ecmaVersion===6&&t&&h&&h.type==="RestElement"&&h.argument.type!=="Identifier"&&this.unexpected(h.argument.start)}return e},he.parseSpread=function(e){var t=this.startNode();return this.next(),t.argument=this.parseMaybeAssign(!1,e),this.finishNode(t,"SpreadElement")},he.parseRestBinding=function(){var e=this.startNode();return this.next(),this.options.ecmaVersion===6&&this.type!==i.name&&this.unexpected(),e.argument=this.parseBindingAtom(),this.finishNode(e,"RestElement")},he.parseBindingAtom=function(){if(this.options.ecmaVersion>=6)switch(this.type){case i.bracketL:var e=this.startNode();return this.next(),e.elements=this.parseBindingList(i.bracketR,!0,!0),this.finishNode(e,"ArrayPattern");case i.braceL:return this.parseObj(!0)}return this.parseIdent()},he.parseBindingList=function(e,t,r){for(var s=[],n=!0;!this.eat(e);)if(n?n=!1:this.expect(i.comma),t&&this.type===i.comma)s.push(null);else{if(r&&this.afterTrailingComma(e))break;if(this.type===i.ellipsis){var h=this.parseRestBinding();this.parseBindingListItem(h),s.push(h),this.type===i.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.expect(e);break}else{var c=this.parseMaybeDefault(this.start,this.startLoc);this.parseBindingListItem(c),s.push(c)}}return s},he.parseBindingListItem=function(e){return e},he.parseMaybeDefault=function(e,t,r){if(r=r||this.parseBindingAtom(),this.options.ecmaVersion<6||!this.eat(i.eq))return r;var s=this.startNodeAt(e,t);return s.left=r,s.right=this.parseMaybeAssign(),this.finishNode(s,"AssignmentPattern")},he.checkLValSimple=function(e,t,r){t===void 0&&(t=Ge);var s=t!==Ge;switch(e.type){case"Identifier":this.strict&&this.reservedWordsStrictBind.test(e.name)&&this.raiseRecoverable(e.start,(s?"Binding ":"Assigning to ")+e.name+" in strict mode"),s&&(t===ve&&e.name==="let"&&this.raiseRecoverable(e.start,"let is disallowed as a lexically bound name"),r&&(P(r,e.name)&&this.raiseRecoverable(e.start,"Argument name clash"),r[e.name]=!0),t!==Ar&&this.declareName(e.name,t,e.start));break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":s&&this.raiseRecoverable(e.start,"Binding member expression");break;case"ParenthesizedExpression":return s&&this.raiseRecoverable(e.start,"Binding parenthesized expression"),this.checkLValSimple(e.expression,t,r);default:this.raise(e.start,(s?"Binding":"Assigning to")+" rvalue")}},he.checkLValPattern=function(e,t,r){switch(t===void 0&&(t=Ge),e.type){case"ObjectPattern":for(var s=0,n=e.properties;s<n.length;s+=1){var h=n[s];this.checkLValInnerPattern(h,t,r)}break;case"ArrayPattern":for(var c=0,m=e.elements;c<m.length;c+=1){var A=m[c];A&&this.checkLValInnerPattern(A,t,r)}break;default:this.checkLValSimple(e,t,r)}},he.checkLValInnerPattern=function(e,t,r){switch(t===void 0&&(t=Ge),e.type){case"Property":this.checkLValInnerPattern(e.value,t,r);break;case"AssignmentPattern":this.checkLValPattern(e.left,t,r);break;case"RestElement":this.checkLValPattern(e.argument,t,r);break;default:this.checkLValPattern(e,t,r)}};var ue=function(t,r,s,n,h){this.token=t,this.isExpr=!!r,this.preserveSpace=!!s,this.override=n,this.generator=!!h},$={b_stat:new ue("{",!1),b_expr:new ue("{",!0),b_tmpl:new ue("${",!1),p_stat:new ue("(",!1),p_expr:new ue("(",!0),q_tmpl:new ue("`",!0,!0,function(e){return e.tryReadTemplateToken()}),f_stat:new ue("function",!1),f_expr:new ue("function",!0),f_expr_gen:new ue("function",!0,!1,null,!0),f_gen:new ue("function",!1,!1,null,!0)},Fe=Y.prototype;Fe.initialContext=function(){return[$.b_stat]},Fe.curContext=function(){return this.context[this.context.length-1]},Fe.braceIsBlock=function(e){var t=this.curContext();return t===$.f_expr||t===$.f_stat?!0:e===i.colon&&(t===$.b_stat||t===$.b_expr)?!t.isExpr:e===i._return||e===i.name&&this.exprAllowed?S.test(this.input.slice(this.lastTokEnd,this.start)):e===i._else||e===i.semi||e===i.eof||e===i.parenR||e===i.arrow?!0:e===i.braceL?t===$.b_stat:e===i._var||e===i._const||e===i.name?!1:!this.exprAllowed},Fe.inGeneratorContext=function(){for(var e=this.context.length-1;e>=1;e--){var t=this.context[e];if(t.token==="function")return t.generator}return!1},Fe.updateContext=function(e){var t,r=this.type;r.keyword&&e===i.dot?this.exprAllowed=!1:(t=r.updateContext)?t.call(this,e):this.exprAllowed=r.beforeExpr},Fe.overrideContext=function(e){this.curContext()!==e&&(this.context[this.context.length-1]=e)},i.parenR.updateContext=i.braceR.updateContext=function(){if(this.context.length===1){this.exprAllowed=!0;return}var e=this.context.pop();e===$.b_stat&&this.curContext().token==="function"&&(e=this.context.pop()),this.exprAllowed=!e.isExpr},i.braceL.updateContext=function(e){this.context.push(this.braceIsBlock(e)?$.b_stat:$.b_expr),this.exprAllowed=!0},i.dollarBraceL.updateContext=function(){this.context.push($.b_tmpl),this.exprAllowed=!0},i.parenL.updateContext=function(e){var t=e===i._if||e===i._for||e===i._with||e===i._while;this.context.push(t?$.p_stat:$.p_expr),this.exprAllowed=!0},i.incDec.updateContext=function(){},i._function.updateContext=i._class.updateContext=function(e){e.beforeExpr&&e!==i._else&&!(e===i.semi&&this.curContext()!==$.p_stat)&&!(e===i._return&&S.test(this.input.slice(this.lastTokEnd,this.start)))&&!((e===i.colon||e===i.braceL)&&this.curContext()===$.b_stat)?this.context.push($.f_expr):this.context.push($.f_stat),this.exprAllowed=!1},i.backQuote.updateContext=function(){this.curContext()===$.q_tmpl?this.context.pop():this.context.push($.q_tmpl),this.exprAllowed=!1},i.star.updateContext=function(e){if(e===i._function){var t=this.context.length-1;this.context[t]===$.f_expr?this.context[t]=$.f_expr_gen:this.context[t]=$.f_gen}this.exprAllowed=!0},i.name.updateContext=function(e){var t=!1;this.options.ecmaVersion>=6&&e!==i.dot&&(this.value==="of"&&!this.exprAllowed||this.value==="yield"&&this.inGeneratorContext())&&(t=!0),this.exprAllowed=t};var M=Y.prototype;M.checkPropClash=function(e,t,r){if(!(this.options.ecmaVersion>=9&&e.type==="SpreadElement")&&!(this.options.ecmaVersion>=6&&(e.computed||e.method||e.shorthand))){var s=e.key,n;switch(s.type){case"Identifier":n=s.name;break;case"Literal":n=String(s.value);break;default:return}var h=e.kind;if(this.options.ecmaVersion>=6){n==="__proto__"&&h==="init"&&(t.proto&&(r?r.doubleProto<0&&(r.doubleProto=s.start):this.raiseRecoverable(s.start,"Redefinition of __proto__ property")),t.proto=!0);return}n="$"+n;var c=t[n];if(c){var m;h==="init"?m=this.strict&&c.init||c.get||c.set:m=c.init||c[h],m&&this.raiseRecoverable(s.start,"Redefinition of property")}else c=t[n]={init:!1,get:!1,set:!1};c[h]=!0}},M.parseExpression=function(e,t){var r=this.start,s=this.startLoc,n=this.parseMaybeAssign(e,t);if(this.type===i.comma){var h=this.startNodeAt(r,s);for(h.expressions=[n];this.eat(i.comma);)h.expressions.push(this.parseMaybeAssign(e,t));return this.finishNode(h,"SequenceExpression")}return n},M.parseMaybeAssign=function(e,t,r){if(this.isContextual("yield")){if(this.inGenerator)return this.parseYield(e);this.exprAllowed=!1}var s=!1,n=-1,h=-1,c=-1;t?(n=t.parenthesizedAssign,h=t.trailingComma,c=t.doubleProto,t.parenthesizedAssign=t.trailingComma=-1):(t=new He,s=!0);var m=this.start,A=this.startLoc;(this.type===i.parenL||this.type===i.name)&&(this.potentialArrowAt=this.start,this.potentialArrowInForAwait=e==="await");var q=this.parseMaybeConditional(e,t);if(r&&(q=r.call(this,q,m,A)),this.type.isAssign){var W=this.startNodeAt(m,A);return W.operator=this.value,this.type===i.eq&&(q=this.toAssignable(q,!1,t)),s||(t.parenthesizedAssign=t.trailingComma=t.doubleProto=-1),t.shorthandAssign>=q.start&&(t.shorthandAssign=-1),this.type===i.eq?this.checkLValPattern(q):this.checkLValSimple(q),W.left=q,this.next(),W.right=this.parseMaybeAssign(e),c>-1&&(t.doubleProto=c),this.finishNode(W,"AssignmentExpression")}else s&&this.checkExpressionErrors(t,!0);return n>-1&&(t.parenthesizedAssign=n),h>-1&&(t.trailingComma=h),q},M.parseMaybeConditional=function(e,t){var r=this.start,s=this.startLoc,n=this.parseExprOps(e,t);if(this.checkExpressionErrors(t))return n;if(this.eat(i.question)){var h=this.startNodeAt(r,s);return h.test=n,h.consequent=this.parseMaybeAssign(),this.expect(i.colon),h.alternate=this.parseMaybeAssign(e),this.finishNode(h,"ConditionalExpression")}return n},M.parseExprOps=function(e,t){var r=this.start,s=this.startLoc,n=this.parseMaybeUnary(t,!1,!1,e);return this.checkExpressionErrors(t)||n.start===r&&n.type==="ArrowFunctionExpression"?n:this.parseExprOp(n,r,s,-1,e)},M.parseExprOp=function(e,t,r,s,n){var h=this.type.binop;if(h!=null&&(!n||this.type!==i._in)&&h>s){var c=this.type===i.logicalOR||this.type===i.logicalAND,m=this.type===i.coalesce;m&&(h=i.logicalAND.binop);var A=this.value;this.next();var q=this.start,W=this.startLoc,re=this.parseExprOp(this.parseMaybeUnary(null,!1,!1,n),q,W,h,n),Se=this.buildBinary(t,r,e,re,A,c||m);return(c&&this.type===i.coalesce||m&&(this.type===i.logicalOR||this.type===i.logicalAND))&&this.raiseRecoverable(this.start,"Logical expressions and coalesce expressions cannot be mixed. Wrap either by parentheses"),this.parseExprOp(Se,t,r,s,n)}return e},M.buildBinary=function(e,t,r,s,n,h){s.type==="PrivateIdentifier"&&this.raise(s.start,"Private identifier can only be left side of binary expression");var c=this.startNodeAt(e,t);return c.left=r,c.operator=n,c.right=s,this.finishNode(c,h?"LogicalExpression":"BinaryExpression")},M.parseMaybeUnary=function(e,t,r,s){var n=this.start,h=this.startLoc,c;if(this.isContextual("await")&&this.canAwait)c=this.parseAwait(s),t=!0;else if(this.type.prefix){var m=this.startNode(),A=this.type===i.incDec;m.operator=this.value,m.prefix=!0,this.next(),m.argument=this.parseMaybeUnary(null,!0,A,s),this.checkExpressionErrors(e,!0),A?this.checkLValSimple(m.argument):this.strict&&m.operator==="delete"&&m.argument.type==="Identifier"?this.raiseRecoverable(m.start,"Deleting local variable in strict mode"):m.operator==="delete"&&Er(m.argument)?this.raiseRecoverable(m.start,"Private fields can not be deleted"):t=!0,c=this.finishNode(m,A?"UpdateExpression":"UnaryExpression")}else if(!t&&this.type===i.privateId)(s||this.privateNameStack.length===0)&&this.unexpected(),c=this.parsePrivateIdent(),this.type!==i._in&&this.unexpected();else{if(c=this.parseExprSubscripts(e,s),this.checkExpressionErrors(e))return c;for(;this.type.postfix&&!this.canInsertSemicolon();){var q=this.startNodeAt(n,h);q.operator=this.value,q.prefix=!1,q.argument=c,this.checkLValSimple(c),this.next(),c=this.finishNode(q,"UpdateExpression")}}if(!r&&this.eat(i.starstar))if(t)this.unexpected(this.lastTokStart);else return this.buildBinary(n,h,c,this.parseMaybeUnary(null,!1,!1,s),"**",!1);else return c};function Er(e){return e.type==="MemberExpression"&&e.property.type==="PrivateIdentifier"||e.type==="ChainExpression"&&Er(e.expression)}M.parseExprSubscripts=function(e,t){var r=this.start,s=this.startLoc,n=this.parseExprAtom(e,t);if(n.type==="ArrowFunctionExpression"&&this.input.slice(this.lastTokStart,this.lastTokEnd)!==")")return n;var h=this.parseSubscripts(n,r,s,!1,t);return e&&h.type==="MemberExpression"&&(e.parenthesizedAssign>=h.start&&(e.parenthesizedAssign=-1),e.parenthesizedBind>=h.start&&(e.parenthesizedBind=-1),e.trailingComma>=h.start&&(e.trailingComma=-1)),h},M.parseSubscripts=function(e,t,r,s,n){for(var h=this.options.ecmaVersion>=8&&e.type==="Identifier"&&e.name==="async"&&this.lastTokEnd===e.end&&!this.canInsertSemicolon()&&e.end-e.start===5&&this.potentialArrowAt===e.start,c=!1;;){var m=this.parseSubscript(e,t,r,s,h,c,n);if(m.optional&&(c=!0),m===e||m.type==="ArrowFunctionExpression"){if(c){var A=this.startNodeAt(t,r);A.expression=m,m=this.finishNode(A,"ChainExpression")}return m}e=m}},M.parseSubscript=function(e,t,r,s,n,h,c){var m=this.options.ecmaVersion>=11,A=m&&this.eat(i.questionDot);s&&A&&this.raise(this.lastTokStart,"Optional chaining cannot appear in the callee of new expressions");var q=this.eat(i.bracketL);if(q||A&&this.type!==i.parenL&&this.type!==i.backQuote||this.eat(i.dot)){var W=this.startNodeAt(t,r);W.object=e,q?(W.property=this.parseExpression(),this.expect(i.bracketR)):this.type===i.privateId&&e.type!=="Super"?W.property=this.parsePrivateIdent():W.property=this.parseIdent(this.options.allowReserved!=="never"),W.computed=!!q,m&&(W.optional=A),e=this.finishNode(W,"MemberExpression")}else if(!s&&this.eat(i.parenL)){var re=new He,Se=this.yieldPos,qe=this.awaitPos,Be=this.awaitIdentPos;this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0;var $e=this.parseExprList(i.parenR,this.options.ecmaVersion>=8,!1,re);if(n&&!A&&!this.canInsertSemicolon()&&this.eat(i.arrow))return this.checkPatternErrors(re,!1),this.checkYieldAwaitInDefaultParams(),this.awaitIdentPos>0&&this.raise(this.awaitIdentPos,"Cannot use 'await' as identifier inside an async function"),this.yieldPos=Se,this.awaitPos=qe,this.awaitIdentPos=Be,this.parseArrowExpression(this.startNodeAt(t,r),$e,!0,c);this.checkExpressionErrors(re,!0),this.yieldPos=Se||this.yieldPos,this.awaitPos=qe||this.awaitPos,this.awaitIdentPos=Be||this.awaitIdentPos;var Ie=this.startNodeAt(t,r);Ie.callee=e,Ie.arguments=$e,m&&(Ie.optional=A),e=this.finishNode(Ie,"CallExpression")}else if(this.type===i.backQuote){(A||h)&&this.raise(this.start,"Optional chaining cannot appear in the tag of tagged template expressions");var Te=this.startNodeAt(t,r);Te.tag=e,Te.quasi=this.parseTemplate({isTagged:!0}),e=this.finishNode(Te,"TaggedTemplateExpression")}return e},M.parseExprAtom=function(e,t){this.type===i.slash&&this.readRegexp();var r,s=this.potentialArrowAt===this.start;switch(this.type){case i._super:return this.allowSuper||this.raise(this.start,"'super' keyword outside a method"),r=this.startNode(),this.next(),this.type===i.parenL&&!this.allowDirectSuper&&this.raise(r.start,"super() call outside constructor of a subclass"),this.type!==i.dot&&this.type!==i.bracketL&&this.type!==i.parenL&&this.unexpected(),this.finishNode(r,"Super");case i._this:return r=this.startNode(),this.next(),this.finishNode(r,"ThisExpression");case i.name:var n=this.start,h=this.startLoc,c=this.containsEsc,m=this.parseIdent(!1);if(this.options.ecmaVersion>=8&&!c&&m.name==="async"&&!this.canInsertSemicolon()&&this.eat(i._function))return this.overrideContext($.f_expr),this.parseFunction(this.startNodeAt(n,h),0,!1,!0,t);if(s&&!this.canInsertSemicolon()){if(this.eat(i.arrow))return this.parseArrowExpression(this.startNodeAt(n,h),[m],!1,t);if(this.options.ecmaVersion>=8&&m.name==="async"&&this.type===i.name&&!c&&(!this.potentialArrowInForAwait||this.value!=="of"||this.containsEsc))return m=this.parseIdent(!1),(this.canInsertSemicolon()||!this.eat(i.arrow))&&this.unexpected(),this.parseArrowExpression(this.startNodeAt(n,h),[m],!0,t)}return m;case i.regexp:var A=this.value;return r=this.parseLiteral(A.value),r.regex={pattern:A.pattern,flags:A.flags},r;case i.num:case i.string:return this.parseLiteral(this.value);case i._null:case i._true:case i._false:return r=this.startNode(),r.value=this.type===i._null?null:this.type===i._true,r.raw=this.type.keyword,this.next(),this.finishNode(r,"Literal");case i.parenL:var q=this.start,W=this.parseParenAndDistinguishExpression(s,t);return e&&(e.parenthesizedAssign<0&&!this.isSimpleAssignTarget(W)&&(e.parenthesizedAssign=q),e.parenthesizedBind<0&&(e.parenthesizedBind=q)),W;case i.bracketL:return r=this.startNode(),this.next(),r.elements=this.parseExprList(i.bracketR,!0,!0,e),this.finishNode(r,"ArrayExpression");case i.braceL:return this.overrideContext($.b_expr),this.parseObj(!1,e);case i._function:return r=this.startNode(),this.next(),this.parseFunction(r,0);case i._class:return this.parseClass(this.startNode(),!1);case i._new:return this.parseNew();case i.backQuote:return this.parseTemplate();case i._import:return this.options.ecmaVersion>=11?this.parseExprImport():this.unexpected();default:this.unexpected()}},M.parseExprImport=function(){var e=this.startNode();this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword import");var t=this.parseIdent(!0);switch(this.type){case i.parenL:return this.parseDynamicImport(e);case i.dot:return e.meta=t,this.parseImportMeta(e);default:this.unexpected()}},M.parseDynamicImport=function(e){if(this.next(),e.source=this.parseMaybeAssign(),!this.eat(i.parenR)){var t=this.start;this.eat(i.comma)&&this.eat(i.parenR)?this.raiseRecoverable(t,"Trailing comma is not allowed in import()"):this.unexpected(t)}return this.finishNode(e,"ImportExpression")},M.parseImportMeta=function(e){this.next();var t=this.containsEsc;return e.property=this.parseIdent(!0),e.property.name!=="meta"&&this.raiseRecoverable(e.property.start,"The only valid meta property for import is 'import.meta'"),t&&this.raiseRecoverable(e.start,"'import.meta' must not contain escaped characters"),this.options.sourceType!=="module"&&!this.options.allowImportExportEverywhere&&this.raiseRecoverable(e.start,"Cannot use 'import.meta' outside a module"),this.finishNode(e,"MetaProperty")},M.parseLiteral=function(e){var t=this.startNode();return t.value=e,t.raw=this.input.slice(this.start,this.end),t.raw.charCodeAt(t.raw.length-1)===110&&(t.bigint=t.raw.slice(0,-1).replace(/_/g,"")),this.next(),this.finishNode(t,"Literal")},M.parseParenExpression=function(){this.expect(i.parenL);var e=this.parseExpression();return this.expect(i.parenR),e},M.parseParenAndDistinguishExpression=function(e,t){var r=this.start,s=this.startLoc,n,h=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var c=this.start,m=this.startLoc,A=[],q=!0,W=!1,re=new He,Se=this.yieldPos,qe=this.awaitPos,Be;for(this.yieldPos=0,this.awaitPos=0;this.type!==i.parenR;)if(q?q=!1:this.expect(i.comma),h&&this.afterTrailingComma(i.parenR,!0)){W=!0;break}else if(this.type===i.ellipsis){Be=this.start,A.push(this.parseParenItem(this.parseRestBinding())),this.type===i.comma&&this.raise(this.start,"Comma is not permitted after the rest element");break}else A.push(this.parseMaybeAssign(!1,re,this.parseParenItem));var $e=this.lastTokEnd,Ie=this.lastTokEndLoc;if(this.expect(i.parenR),e&&!this.canInsertSemicolon()&&this.eat(i.arrow))return this.checkPatternErrors(re,!1),this.checkYieldAwaitInDefaultParams(),this.yieldPos=Se,this.awaitPos=qe,this.parseParenArrowList(r,s,A,t);(!A.length||W)&&this.unexpected(this.lastTokStart),Be&&this.unexpected(Be),this.checkExpressionErrors(re,!0),this.yieldPos=Se||this.yieldPos,this.awaitPos=qe||this.awaitPos,A.length>1?(n=this.startNodeAt(c,m),n.expressions=A,this.finishNodeAt(n,"SequenceExpression",$e,Ie)):n=A[0]}else n=this.parseParenExpression();if(this.options.preserveParens){var Te=this.startNodeAt(r,s);return Te.expression=n,this.finishNode(Te,"ParenthesizedExpression")}else return n},M.parseParenItem=function(e){return e},M.parseParenArrowList=function(e,t,r,s){return this.parseArrowExpression(this.startNodeAt(e,t),r,!1,s)};var Ja=[];M.parseNew=function(){this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword new");var e=this.startNode(),t=this.parseIdent(!0);if(this.options.ecmaVersion>=6&&this.eat(i.dot)){e.meta=t;var r=this.containsEsc;return e.property=this.parseIdent(!0),e.property.name!=="target"&&this.raiseRecoverable(e.property.start,"The only valid meta property for new is 'new.target'"),r&&this.raiseRecoverable(e.start,"'new.target' must not contain escaped characters"),this.allowNewDotTarget||this.raiseRecoverable(e.start,"'new.target' can only be used in functions and class static block"),this.finishNode(e,"MetaProperty")}var s=this.start,n=this.startLoc,h=this.type===i._import;return e.callee=this.parseSubscripts(this.parseExprAtom(),s,n,!0,!1),h&&e.callee.type==="ImportExpression"&&this.raise(s,"Cannot use new with import()"),this.eat(i.parenL)?e.arguments=this.parseExprList(i.parenR,this.options.ecmaVersion>=8,!1):e.arguments=Ja,this.finishNode(e,"NewExpression")},M.parseTemplateElement=function(e){var t=e.isTagged,r=this.startNode();return this.type===i.invalidTemplate?(t||this.raiseRecoverable(this.start,"Bad escape sequence in untagged template literal"),r.value={raw:this.value,cooked:null}):r.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,`
`),cooked:this.value},this.next(),r.tail=this.type===i.backQuote,this.finishNode(r,"TemplateElement")},M.parseTemplate=function(e){e===void 0&&(e={});var t=e.isTagged;t===void 0&&(t=!1);var r=this.startNode();this.next(),r.expressions=[];var s=this.parseTemplateElement({isTagged:t});for(r.quasis=[s];!s.tail;)this.type===i.eof&&this.raise(this.pos,"Unterminated template literal"),this.expect(i.dollarBraceL),r.expressions.push(this.parseExpression()),this.expect(i.braceR),r.quasis.push(s=this.parseTemplateElement({isTagged:t}));return this.next(),this.finishNode(r,"TemplateLiteral")},M.isAsyncProp=function(e){return!e.computed&&e.key.type==="Identifier"&&e.key.name==="async"&&(this.type===i.name||this.type===i.num||this.type===i.string||this.type===i.bracketL||this.type.keyword||this.options.ecmaVersion>=9&&this.type===i.star)&&!S.test(this.input.slice(this.lastTokEnd,this.start))},M.parseObj=function(e,t){var r=this.startNode(),s=!0,n={};for(r.properties=[],this.next();!this.eat(i.braceR);){if(s)s=!1;else if(this.expect(i.comma),this.options.ecmaVersion>=5&&this.afterTrailingComma(i.braceR))break;var h=this.parseProperty(e,t);e||this.checkPropClash(h,n,t),r.properties.push(h)}return this.finishNode(r,e?"ObjectPattern":"ObjectExpression")},M.parseProperty=function(e,t){var r=this.startNode(),s,n,h,c;if(this.options.ecmaVersion>=9&&this.eat(i.ellipsis))return e?(r.argument=this.parseIdent(!1),this.type===i.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.finishNode(r,"RestElement")):(r.argument=this.parseMaybeAssign(!1,t),this.type===i.comma&&t&&t.trailingComma<0&&(t.trailingComma=this.start),this.finishNode(r,"SpreadElement"));this.options.ecmaVersion>=6&&(r.method=!1,r.shorthand=!1,(e||t)&&(h=this.start,c=this.startLoc),e||(s=this.eat(i.star)));var m=this.containsEsc;return this.parsePropertyName(r),!e&&!m&&this.options.ecmaVersion>=8&&!s&&this.isAsyncProp(r)?(n=!0,s=this.options.ecmaVersion>=9&&this.eat(i.star),this.parsePropertyName(r,t)):n=!1,this.parsePropertyValue(r,e,s,n,h,c,t,m),this.finishNode(r,"Property")},M.parsePropertyValue=function(e,t,r,s,n,h,c,m){if((r||s)&&this.type===i.colon&&this.unexpected(),this.eat(i.colon))e.value=t?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(!1,c),e.kind="init";else if(this.options.ecmaVersion>=6&&this.type===i.parenL)t&&this.unexpected(),e.kind="init",e.method=!0,e.value=this.parseMethod(r,s);else if(!t&&!m&&this.options.ecmaVersion>=5&&!e.computed&&e.key.type==="Identifier"&&(e.key.name==="get"||e.key.name==="set")&&this.type!==i.comma&&this.type!==i.braceR&&this.type!==i.eq){(r||s)&&this.unexpected(),e.kind=e.key.name,this.parsePropertyName(e),e.value=this.parseMethod(!1);var A=e.kind==="get"?0:1;if(e.value.params.length!==A){var q=e.value.start;e.kind==="get"?this.raiseRecoverable(q,"getter should have no params"):this.raiseRecoverable(q,"setter should have exactly one param")}else e.kind==="set"&&e.value.params[0].type==="RestElement"&&this.raiseRecoverable(e.value.params[0].start,"Setter cannot use rest params")}else this.options.ecmaVersion>=6&&!e.computed&&e.key.type==="Identifier"?((r||s)&&this.unexpected(),this.checkUnreserved(e.key),e.key.name==="await"&&!this.awaitIdentPos&&(this.awaitIdentPos=n),e.kind="init",t?e.value=this.parseMaybeDefault(n,h,this.copyNode(e.key)):this.type===i.eq&&c?(c.shorthandAssign<0&&(c.shorthandAssign=this.start),e.value=this.parseMaybeDefault(n,h,this.copyNode(e.key))):e.value=this.copyNode(e.key),e.shorthand=!0):this.unexpected()},M.parsePropertyName=function(e){if(this.options.ecmaVersion>=6){if(this.eat(i.bracketL))return e.computed=!0,e.key=this.parseMaybeAssign(),this.expect(i.bracketR),e.key;e.computed=!1}return e.key=this.type===i.num||this.type===i.string?this.parseExprAtom():this.parseIdent(this.options.allowReserved!=="never")},M.initFunction=function(e){e.id=null,this.options.ecmaVersion>=6&&(e.generator=e.expression=!1),this.options.ecmaVersion>=8&&(e.async=!1)},M.parseMethod=function(e,t,r){var s=this.startNode(),n=this.yieldPos,h=this.awaitPos,c=this.awaitIdentPos;return this.initFunction(s),this.options.ecmaVersion>=6&&(s.generator=e),this.options.ecmaVersion>=8&&(s.async=!!t),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(xt(t,s.generator)|vt|(r?gr:0)),this.expect(i.parenL),s.params=this.parseBindingList(i.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams(),this.parseFunctionBody(s,!1,!0,!1),this.yieldPos=n,this.awaitPos=h,this.awaitIdentPos=c,this.finishNode(s,"FunctionExpression")},M.parseArrowExpression=function(e,t,r,s){var n=this.yieldPos,h=this.awaitPos,c=this.awaitIdentPos;return this.enterScope(xt(r,!1)|mr),this.initFunction(e),this.options.ecmaVersion>=8&&(e.async=!!r),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,e.params=this.toAssignableList(t,!0),this.parseFunctionBody(e,!0,!1,s),this.yieldPos=n,this.awaitPos=h,this.awaitIdentPos=c,this.finishNode(e,"ArrowFunctionExpression")},M.parseFunctionBody=function(e,t,r,s){var n=t&&this.type!==i.braceL,h=this.strict,c=!1;if(n)e.body=this.parseMaybeAssign(s),e.expression=!0,this.checkParams(e,!1);else{var m=this.options.ecmaVersion>=7&&!this.isSimpleParamList(e.params);(!h||m)&&(c=this.strictDirective(this.end),c&&m&&this.raiseRecoverable(e.start,"Illegal 'use strict' directive in function with non-simple parameter list"));var A=this.labels;this.labels=[],c&&(this.strict=!0),this.checkParams(e,!h&&!c&&!t&&!r&&this.isSimpleParamList(e.params)),this.strict&&e.id&&this.checkLValSimple(e.id,Ar),e.body=this.parseBlock(!1,void 0,c&&!h),e.expression=!1,this.adaptDirectivePrologue(e.body.body),this.labels=A}this.exitScope()},M.isSimpleParamList=function(e){for(var t=0,r=e;t<r.length;t+=1){var s=r[t];if(s.type!=="Identifier")return!1}return!0},M.checkParams=function(e,t){for(var r=Object.create(null),s=0,n=e.params;s<n.length;s+=1){var h=n[s];this.checkLValInnerPattern(h,yt,t?null:r)}},M.parseExprList=function(e,t,r,s){for(var n=[],h=!0;!this.eat(e);){if(h)h=!1;else if(this.expect(i.comma),t&&this.afterTrailingComma(e))break;var c=void 0;r&&this.type===i.comma?c=null:this.type===i.ellipsis?(c=this.parseSpread(s),s&&this.type===i.comma&&s.trailingComma<0&&(s.trailingComma=this.start)):c=this.parseMaybeAssign(!1,s),n.push(c)}return n},M.checkUnreserved=function(e){var t=e.start,r=e.end,s=e.name;if(this.inGenerator&&s==="yield"&&this.raiseRecoverable(t,"Cannot use 'yield' as identifier inside a generator"),this.inAsync&&s==="await"&&this.raiseRecoverable(t,"Cannot use 'await' as identifier inside an async function"),this.currentThisScope().inClassFieldInit&&s==="arguments"&&this.raiseRecoverable(t,"Cannot use 'arguments' in class field initializer"),this.inClassStaticBlock&&(s==="arguments"||s==="await")&&this.raise(t,"Cannot use "+s+" in class static initialization block"),this.keywords.test(s)&&this.raise(t,"Unexpected keyword '"+s+"'"),!(this.options.ecmaVersion<6&&this.input.slice(t,r).indexOf("\\")!==-1)){var n=this.strict?this.reservedWordsStrict:this.reservedWords;n.test(s)&&(!this.inAsync&&s==="await"&&this.raiseRecoverable(t,"Cannot use keyword 'await' outside an async function"),this.raiseRecoverable(t,"The keyword '"+s+"' is reserved"))}},M.parseIdent=function(e,t){var r=this.startNode();return this.type===i.name?r.name=this.value:this.type.keyword?(r.name=this.type.keyword,(r.name==="class"||r.name==="function")&&(this.lastTokEnd!==this.lastTokStart+1||this.input.charCodeAt(this.lastTokStart)!==46)&&this.context.pop()):this.unexpected(),this.next(!!e),this.finishNode(r,"Identifier"),e||(this.checkUnreserved(r),r.name==="await"&&!this.awaitIdentPos&&(this.awaitIdentPos=r.start)),r},M.parsePrivateIdent=function(){var e=this.startNode();return this.type===i.privateId?e.name=this.value:this.unexpected(),this.next(),this.finishNode(e,"PrivateIdentifier"),this.privateNameStack.length===0?this.raise(e.start,"Private field '#"+e.name+"' must be declared in an enclosing class"):this.privateNameStack[this.privateNameStack.length-1].used.push(e),e},M.parseYield=function(e){this.yieldPos||(this.yieldPos=this.start);var t=this.startNode();return this.next(),this.type===i.semi||this.canInsertSemicolon()||this.type!==i.star&&!this.type.startsExpr?(t.delegate=!1,t.argument=null):(t.delegate=this.eat(i.star),t.argument=this.parseMaybeAssign(e)),this.finishNode(t,"YieldExpression")},M.parseAwait=function(e){this.awaitPos||(this.awaitPos=this.start);var t=this.startNode();return this.next(),t.argument=this.parseMaybeUnary(null,!0,!1,e),this.finishNode(t,"AwaitExpression")};var Xe=Y.prototype;Xe.raise=function(e,t){var r=ae(this.input,e);t+=" ("+r.line+":"+r.column+")";var s=new SyntaxError(t);throw s.pos=e,s.loc=r,s.raisedAt=this.pos,s},Xe.raiseRecoverable=Xe.raise,Xe.curPosition=function(){if(this.options.locations)return new H(this.curLine,this.pos-this.lineStart)};var Ee=Y.prototype,Qa=function(t){this.flags=t,this.var=[],this.lexical=[],this.functions=[],this.inClassFieldInit=!1};Ee.enterScope=function(e){this.scopeStack.push(new Qa(e))},Ee.exitScope=function(){this.scopeStack.pop()},Ee.treatFunctionsAsVarInScope=function(e){return e.flags&Ce||!this.inModule&&e.flags&_e},Ee.declareName=function(e,t,r){var s=!1;if(t===ve){var n=this.currentScope();s=n.lexical.indexOf(e)>-1||n.functions.indexOf(e)>-1||n.var.indexOf(e)>-1,n.lexical.push(e),this.inModule&&n.flags&_e&&delete this.undefinedExports[e]}else if(t===yr){var h=this.currentScope();h.lexical.push(e)}else if(t===xr){var c=this.currentScope();this.treatFunctionsAsVar?s=c.lexical.indexOf(e)>-1:s=c.lexical.indexOf(e)>-1||c.var.indexOf(e)>-1,c.functions.push(e)}else for(var m=this.scopeStack.length-1;m>=0;--m){var A=this.scopeStack[m];if(A.lexical.indexOf(e)>-1&&!(A.flags&vr&&A.lexical[0]===e)||!this.treatFunctionsAsVarInScope(A)&&A.functions.indexOf(e)>-1){s=!0;break}if(A.var.push(e),this.inModule&&A.flags&_e&&delete this.undefinedExports[e],A.flags&gt)break}s&&this.raiseRecoverable(r,"Identifier '"+e+"' has already been declared")},Ee.checkLocalExport=function(e){this.scopeStack[0].lexical.indexOf(e.name)===-1&&this.scopeStack[0].var.indexOf(e.name)===-1&&(this.undefinedExports[e.name]=e)},Ee.currentScope=function(){return this.scopeStack[this.scopeStack.length-1]},Ee.currentVarScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(t.flags&gt)return t}},Ee.currentThisScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(t.flags&gt&&!(t.flags&mr))return t}};var Re=function(t,r,s){this.type="",this.start=r,this.end=0,t.options.locations&&(this.loc=new te(t,s)),t.options.directSourceFile&&(this.sourceFile=t.options.directSourceFile),t.options.ranges&&(this.range=[r,0])},je=Y.prototype;je.startNode=function(){return new Re(this,this.start,this.startLoc)},je.startNodeAt=function(e,t){return new Re(this,e,t)};function br(e,t,r,s){return e.type=t,e.end=r,this.options.locations&&(e.loc.end=s),this.options.ranges&&(e.range[1]=r),e}je.finishNode=function(e,t){return br.call(this,e,t,this.lastTokEnd,this.lastTokEndLoc)},je.finishNodeAt=function(e,t,r,s){return br.call(this,e,t,r,s)},je.copyNode=function(e){var t=new Re(this,e.start,this.startLoc);for(var r in e)t[r]=e[r];return t};var _r="ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS",Sr=_r+" Extended_Pictographic",wr=Sr,kr=wr+" EBase EComp EMod EPres ExtPict",$a=kr,Ya={9:_r,10:Sr,11:wr,12:kr,13:$a},Fr="Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu",Br="Adlam Adlm Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb",Ir=Br+" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd",Tr=Ir+" Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho",Pr=Tr+" Chorasmian Chrs Diak Dives_Akuru Khitan_Small_Script Kits Yezi Yezidi",Za=Pr+" Cypro_Minoan Cpmn Old_Uyghur Ougr Tangsa Tnsa Toto Vithkuqi Vith",en={9:Br,10:Ir,11:Tr,12:Pr,13:Za},Dr={};function tn(e){var t=Dr[e]={binary:d(Ya[e]+" "+Fr),nonBinary:{General_Category:d(Fr),Script:d(en[e])}};t.nonBinary.Script_Extensions=t.nonBinary.Script,t.nonBinary.gc=t.nonBinary.General_Category,t.nonBinary.sc=t.nonBinary.Script,t.nonBinary.scx=t.nonBinary.Script_Extensions}for(var Et=0,Nr=[9,10,11,12,13];Et<Nr.length;Et+=1){var rn=Nr[Et];tn(rn)}var N=Y.prototype,ge=function(t){this.parser=t,this.validFlags="gim"+(t.options.ecmaVersion>=6?"uy":"")+(t.options.ecmaVersion>=9?"s":"")+(t.options.ecmaVersion>=13?"d":""),this.unicodeProperties=Dr[t.options.ecmaVersion>=13?13:t.options.ecmaVersion],this.source="",this.flags="",this.start=0,this.switchU=!1,this.switchN=!1,this.pos=0,this.lastIntValue=0,this.lastStringValue="",this.lastAssertionIsQuantifiable=!1,this.numCapturingParens=0,this.maxBackReference=0,this.groupNames=[],this.backReferenceNames=[]};ge.prototype.reset=function(t,r,s){var n=s.indexOf("u")!==-1;this.start=t|0,this.source=r+"",this.flags=s,this.switchU=n&&this.parser.options.ecmaVersion>=6,this.switchN=n&&this.parser.options.ecmaVersion>=9},ge.prototype.raise=function(t){this.parser.raiseRecoverable(this.start,"Invalid regular expression: /"+this.source+"/: "+t)},ge.prototype.at=function(t,r){r===void 0&&(r=!1);var s=this.source,n=s.length;if(t>=n)return-1;var h=s.charCodeAt(t);if(!(r||this.switchU)||h<=55295||h>=57344||t+1>=n)return h;var c=s.charCodeAt(t+1);return c>=56320&&c<=57343?(h<<10)+c-56613888:h},ge.prototype.nextIndex=function(t,r){r===void 0&&(r=!1);var s=this.source,n=s.length;if(t>=n)return n;var h=s.charCodeAt(t),c;return!(r||this.switchU)||h<=55295||h>=57344||t+1>=n||(c=s.charCodeAt(t+1))<56320||c>57343?t+1:t+2},ge.prototype.current=function(t){return t===void 0&&(t=!1),this.at(this.pos,t)},ge.prototype.lookahead=function(t){return t===void 0&&(t=!1),this.at(this.nextIndex(this.pos,t),t)},ge.prototype.advance=function(t){t===void 0&&(t=!1),this.pos=this.nextIndex(this.pos,t)},ge.prototype.eat=function(t,r){return r===void 0&&(r=!1),this.current(r)===t?(this.advance(r),!0):!1},N.validateRegExpFlags=function(e){for(var t=e.validFlags,r=e.flags,s=0;s<r.length;s++){var n=r.charAt(s);t.indexOf(n)===-1&&this.raise(e.start,"Invalid regular expression flag"),r.indexOf(n,s+1)>-1&&this.raise(e.start,"Duplicate regular expression flag")}},N.validateRegExpPattern=function(e){this.regexp_pattern(e),!e.switchN&&this.options.ecmaVersion>=9&&e.groupNames.length>0&&(e.switchN=!0,this.regexp_pattern(e))},N.regexp_pattern=function(e){e.pos=0,e.lastIntValue=0,e.lastStringValue="",e.lastAssertionIsQuantifiable=!1,e.numCapturingParens=0,e.maxBackReference=0,e.groupNames.length=0,e.backReferenceNames.length=0,this.regexp_disjunction(e),e.pos!==e.source.length&&(e.eat(41)&&e.raise("Unmatched ')'"),(e.eat(93)||e.eat(125))&&e.raise("Lone quantifier brackets")),e.maxBackReference>e.numCapturingParens&&e.raise("Invalid escape");for(var t=0,r=e.backReferenceNames;t<r.length;t+=1){var s=r[t];e.groupNames.indexOf(s)===-1&&e.raise("Invalid named capture referenced")}},N.regexp_disjunction=function(e){for(this.regexp_alternative(e);e.eat(124);)this.regexp_alternative(e);this.regexp_eatQuantifier(e,!0)&&e.raise("Nothing to repeat"),e.eat(123)&&e.raise("Lone quantifier brackets")},N.regexp_alternative=function(e){for(;e.pos<e.source.length&&this.regexp_eatTerm(e););},N.regexp_eatTerm=function(e){return this.regexp_eatAssertion(e)?(e.lastAssertionIsQuantifiable&&this.regexp_eatQuantifier(e)&&e.switchU&&e.raise("Invalid quantifier"),!0):(e.switchU?this.regexp_eatAtom(e):this.regexp_eatExtendedAtom(e))?(this.regexp_eatQuantifier(e),!0):!1},N.regexp_eatAssertion=function(e){var t=e.pos;if(e.lastAssertionIsQuantifiable=!1,e.eat(94)||e.eat(36))return!0;if(e.eat(92)){if(e.eat(66)||e.eat(98))return!0;e.pos=t}if(e.eat(40)&&e.eat(63)){var r=!1;if(this.options.ecmaVersion>=9&&(r=e.eat(60)),e.eat(61)||e.eat(33))return this.regexp_disjunction(e),e.eat(41)||e.raise("Unterminated group"),e.lastAssertionIsQuantifiable=!r,!0}return e.pos=t,!1},N.regexp_eatQuantifier=function(e,t){return t===void 0&&(t=!1),this.regexp_eatQuantifierPrefix(e,t)?(e.eat(63),!0):!1},N.regexp_eatQuantifierPrefix=function(e,t){return e.eat(42)||e.eat(43)||e.eat(63)||this.regexp_eatBracedQuantifier(e,t)},N.regexp_eatBracedQuantifier=function(e,t){var r=e.pos;if(e.eat(123)){var s=0,n=-1;if(this.regexp_eatDecimalDigits(e)&&(s=e.lastIntValue,e.eat(44)&&this.regexp_eatDecimalDigits(e)&&(n=e.lastIntValue),e.eat(125)))return n!==-1&&n<s&&!t&&e.raise("numbers out of order in {} quantifier"),!0;e.switchU&&!t&&e.raise("Incomplete quantifier"),e.pos=r}return!1},N.regexp_eatAtom=function(e){return this.regexp_eatPatternCharacters(e)||e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)},N.regexp_eatReverseSolidusAtomEscape=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatAtomEscape(e))return!0;e.pos=t}return!1},N.regexp_eatUncapturingGroup=function(e){var t=e.pos;if(e.eat(40)){if(e.eat(63)&&e.eat(58)){if(this.regexp_disjunction(e),e.eat(41))return!0;e.raise("Unterminated group")}e.pos=t}return!1},N.regexp_eatCapturingGroup=function(e){if(e.eat(40)){if(this.options.ecmaVersion>=9?this.regexp_groupSpecifier(e):e.current()===63&&e.raise("Invalid group"),this.regexp_disjunction(e),e.eat(41))return e.numCapturingParens+=1,!0;e.raise("Unterminated group")}return!1},N.regexp_eatExtendedAtom=function(e){return e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)||this.regexp_eatInvalidBracedQuantifier(e)||this.regexp_eatExtendedPatternCharacter(e)},N.regexp_eatInvalidBracedQuantifier=function(e){return this.regexp_eatBracedQuantifier(e,!0)&&e.raise("Nothing to repeat"),!1},N.regexp_eatSyntaxCharacter=function(e){var t=e.current();return Or(t)?(e.lastIntValue=t,e.advance(),!0):!1};function Or(e){return e===36||e>=40&&e<=43||e===46||e===63||e>=91&&e<=94||e>=123&&e<=125}N.regexp_eatPatternCharacters=function(e){for(var t=e.pos,r=0;(r=e.current())!==-1&&!Or(r);)e.advance();return e.pos!==t},N.regexp_eatExtendedPatternCharacter=function(e){var t=e.current();return t!==-1&&t!==36&&!(t>=40&&t<=43)&&t!==46&&t!==63&&t!==91&&t!==94&&t!==124?(e.advance(),!0):!1},N.regexp_groupSpecifier=function(e){if(e.eat(63)){if(this.regexp_eatGroupName(e)){e.groupNames.indexOf(e.lastStringValue)!==-1&&e.raise("Duplicate capture group name"),e.groupNames.push(e.lastStringValue);return}e.raise("Invalid group")}},N.regexp_eatGroupName=function(e){if(e.lastStringValue="",e.eat(60)){if(this.regexp_eatRegExpIdentifierName(e)&&e.eat(62))return!0;e.raise("Invalid capture group name")}return!1},N.regexp_eatRegExpIdentifierName=function(e){if(e.lastStringValue="",this.regexp_eatRegExpIdentifierStart(e)){for(e.lastStringValue+=C(e.lastIntValue);this.regexp_eatRegExpIdentifierPart(e);)e.lastStringValue+=C(e.lastIntValue);return!0}return!1},N.regexp_eatRegExpIdentifierStart=function(e){var t=e.pos,r=this.options.ecmaVersion>=11,s=e.current(r);return e.advance(r),s===92&&this.regexp_eatRegExpUnicodeEscapeSequence(e,r)&&(s=e.lastIntValue),sn(s)?(e.lastIntValue=s,!0):(e.pos=t,!1)};function sn(e){return w(e,!0)||e===36||e===95}N.regexp_eatRegExpIdentifierPart=function(e){var t=e.pos,r=this.options.ecmaVersion>=11,s=e.current(r);return e.advance(r),s===92&&this.regexp_eatRegExpUnicodeEscapeSequence(e,r)&&(s=e.lastIntValue),an(s)?(e.lastIntValue=s,!0):(e.pos=t,!1)};function an(e){return G(e,!0)||e===36||e===95||e===8204||e===8205}N.regexp_eatAtomEscape=function(e){return this.regexp_eatBackReference(e)||this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)||e.switchN&&this.regexp_eatKGroupName(e)?!0:(e.switchU&&(e.current()===99&&e.raise("Invalid unicode escape"),e.raise("Invalid escape")),!1)},N.regexp_eatBackReference=function(e){var t=e.pos;if(this.regexp_eatDecimalEscape(e)){var r=e.lastIntValue;if(e.switchU)return r>e.maxBackReference&&(e.maxBackReference=r),!0;if(r<=e.numCapturingParens)return!0;e.pos=t}return!1},N.regexp_eatKGroupName=function(e){if(e.eat(107)){if(this.regexp_eatGroupName(e))return e.backReferenceNames.push(e.lastStringValue),!0;e.raise("Invalid named reference")}return!1},N.regexp_eatCharacterEscape=function(e){return this.regexp_eatControlEscape(e)||this.regexp_eatCControlLetter(e)||this.regexp_eatZero(e)||this.regexp_eatHexEscapeSequence(e)||this.regexp_eatRegExpUnicodeEscapeSequence(e,!1)||!e.switchU&&this.regexp_eatLegacyOctalEscapeSequence(e)||this.regexp_eatIdentityEscape(e)},N.regexp_eatCControlLetter=function(e){var t=e.pos;if(e.eat(99)){if(this.regexp_eatControlLetter(e))return!0;e.pos=t}return!1},N.regexp_eatZero=function(e){return e.current()===48&&!Je(e.lookahead())?(e.lastIntValue=0,e.advance(),!0):!1},N.regexp_eatControlEscape=function(e){var t=e.current();return t===116?(e.lastIntValue=9,e.advance(),!0):t===110?(e.lastIntValue=10,e.advance(),!0):t===118?(e.lastIntValue=11,e.advance(),!0):t===102?(e.lastIntValue=12,e.advance(),!0):t===114?(e.lastIntValue=13,e.advance(),!0):!1},N.regexp_eatControlLetter=function(e){var t=e.current();return Lr(t)?(e.lastIntValue=t%32,e.advance(),!0):!1};function Lr(e){return e>=65&&e<=90||e>=97&&e<=122}N.regexp_eatRegExpUnicodeEscapeSequence=function(e,t){t===void 0&&(t=!1);var r=e.pos,s=t||e.switchU;if(e.eat(117)){if(this.regexp_eatFixedHexDigits(e,4)){var n=e.lastIntValue;if(s&&n>=55296&&n<=56319){var h=e.pos;if(e.eat(92)&&e.eat(117)&&this.regexp_eatFixedHexDigits(e,4)){var c=e.lastIntValue;if(c>=56320&&c<=57343)return e.lastIntValue=(n-55296)*1024+(c-56320)+65536,!0}e.pos=h,e.lastIntValue=n}return!0}if(s&&e.eat(123)&&this.regexp_eatHexDigits(e)&&e.eat(125)&&nn(e.lastIntValue))return!0;s&&e.raise("Invalid unicode escape"),e.pos=r}return!1};function nn(e){return e>=0&&e<=1114111}N.regexp_eatIdentityEscape=function(e){if(e.switchU)return this.regexp_eatSyntaxCharacter(e)?!0:e.eat(47)?(e.lastIntValue=47,!0):!1;var t=e.current();return t!==99&&(!e.switchN||t!==107)?(e.lastIntValue=t,e.advance(),!0):!1},N.regexp_eatDecimalEscape=function(e){e.lastIntValue=0;var t=e.current();if(t>=49&&t<=57){do e.lastIntValue=10*e.lastIntValue+(t-48),e.advance();while((t=e.current())>=48&&t<=57);return!0}return!1},N.regexp_eatCharacterClassEscape=function(e){var t=e.current();if(un(t))return e.lastIntValue=-1,e.advance(),!0;if(e.switchU&&this.options.ecmaVersion>=9&&(t===80||t===112)){if(e.lastIntValue=-1,e.advance(),e.eat(123)&&this.regexp_eatUnicodePropertyValueExpression(e)&&e.eat(125))return!0;e.raise("Invalid property name")}return!1};function un(e){return e===100||e===68||e===115||e===83||e===119||e===87}N.regexp_eatUnicodePropertyValueExpression=function(e){var t=e.pos;if(this.regexp_eatUnicodePropertyName(e)&&e.eat(61)){var r=e.lastStringValue;if(this.regexp_eatUnicodePropertyValue(e)){var s=e.lastStringValue;return this.regexp_validateUnicodePropertyNameAndValue(e,r,s),!0}}if(e.pos=t,this.regexp_eatLoneUnicodePropertyNameOrValue(e)){var n=e.lastStringValue;return this.regexp_validateUnicodePropertyNameOrValue(e,n),!0}return!1},N.regexp_validateUnicodePropertyNameAndValue=function(e,t,r){P(e.unicodeProperties.nonBinary,t)||e.raise("Invalid property name"),e.unicodeProperties.nonBinary[t].test(r)||e.raise("Invalid property value")},N.regexp_validateUnicodePropertyNameOrValue=function(e,t){e.unicodeProperties.binary.test(t)||e.raise("Invalid property name")},N.regexp_eatUnicodePropertyName=function(e){var t=0;for(e.lastStringValue="";Vr(t=e.current());)e.lastStringValue+=C(t),e.advance();return e.lastStringValue!==""};function Vr(e){return Lr(e)||e===95}N.regexp_eatUnicodePropertyValue=function(e){var t=0;for(e.lastStringValue="";on(t=e.current());)e.lastStringValue+=C(t),e.advance();return e.lastStringValue!==""};function on(e){return Vr(e)||Je(e)}N.regexp_eatLoneUnicodePropertyNameOrValue=function(e){return this.regexp_eatUnicodePropertyValue(e)},N.regexp_eatCharacterClass=function(e){if(e.eat(91)){if(e.eat(94),this.regexp_classRanges(e),e.eat(93))return!0;e.raise("Unterminated character class")}return!1},N.regexp_classRanges=function(e){for(;this.regexp_eatClassAtom(e);){var t=e.lastIntValue;if(e.eat(45)&&this.regexp_eatClassAtom(e)){var r=e.lastIntValue;e.switchU&&(t===-1||r===-1)&&e.raise("Invalid character class"),t!==-1&&r!==-1&&t>r&&e.raise("Range out of order in character class")}}},N.regexp_eatClassAtom=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatClassEscape(e))return!0;if(e.switchU){var r=e.current();(r===99||qr(r))&&e.raise("Invalid class escape"),e.raise("Invalid escape")}e.pos=t}var s=e.current();return s!==93?(e.lastIntValue=s,e.advance(),!0):!1},N.regexp_eatClassEscape=function(e){var t=e.pos;if(e.eat(98))return e.lastIntValue=8,!0;if(e.switchU&&e.eat(45))return e.lastIntValue=45,!0;if(!e.switchU&&e.eat(99)){if(this.regexp_eatClassControlLetter(e))return!0;e.pos=t}return this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)},N.regexp_eatClassControlLetter=function(e){var t=e.current();return Je(t)||t===95?(e.lastIntValue=t%32,e.advance(),!0):!1},N.regexp_eatHexEscapeSequence=function(e){var t=e.pos;if(e.eat(120)){if(this.regexp_eatFixedHexDigits(e,2))return!0;e.switchU&&e.raise("Invalid escape"),e.pos=t}return!1},N.regexp_eatDecimalDigits=function(e){var t=e.pos,r=0;for(e.lastIntValue=0;Je(r=e.current());)e.lastIntValue=10*e.lastIntValue+(r-48),e.advance();return e.pos!==t};function Je(e){return e>=48&&e<=57}N.regexp_eatHexDigits=function(e){var t=e.pos,r=0;for(e.lastIntValue=0;Rr(r=e.current());)e.lastIntValue=16*e.lastIntValue+jr(r),e.advance();return e.pos!==t};function Rr(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102}function jr(e){return e>=65&&e<=70?10+(e-65):e>=97&&e<=102?10+(e-97):e-48}N.regexp_eatLegacyOctalEscapeSequence=function(e){if(this.regexp_eatOctalDigit(e)){var t=e.lastIntValue;if(this.regexp_eatOctalDigit(e)){var r=e.lastIntValue;t<=3&&this.regexp_eatOctalDigit(e)?e.lastIntValue=t*64+r*8+e.lastIntValue:e.lastIntValue=t*8+r}else e.lastIntValue=t;return!0}return!1},N.regexp_eatOctalDigit=function(e){var t=e.current();return qr(t)?(e.lastIntValue=t-48,e.advance(),!0):(e.lastIntValue=0,!1)};function qr(e){return e>=48&&e<=55}N.regexp_eatFixedHexDigits=function(e,t){var r=e.pos;e.lastIntValue=0;for(var s=0;s<t;++s){var n=e.current();if(!Rr(n))return e.pos=r,!1;e.lastIntValue=16*e.lastIntValue+jr(n),e.advance()}return!0};var Qe=function(t){this.type=t.type,this.value=t.value,this.start=t.start,this.end=t.end,t.options.locations&&(this.loc=new te(t,t.startLoc,t.endLoc)),t.options.ranges&&(this.range=[t.start,t.end])},z=Y.prototype;z.next=function(e){!e&&this.type.keyword&&this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword "+this.type.keyword),this.options.onToken&&this.options.onToken(new Qe(this)),this.lastTokEnd=this.end,this.lastTokStart=this.start,this.lastTokEndLoc=this.endLoc,this.lastTokStartLoc=this.startLoc,this.nextToken()},z.getToken=function(){return this.next(),new Qe(this)},typeof Symbol<"u"&&(z[Symbol.iterator]=function(){var e=this;return{next:function(){var t=e.getToken();return{done:t.type===i.eof,value:t}}}}),z.nextToken=function(){var e=this.curContext();if((!e||!e.preserveSpace)&&this.skipSpace(),this.start=this.pos,this.options.locations&&(this.startLoc=this.curPosition()),this.pos>=this.input.length)return this.finishToken(i.eof);if(e.override)return e.override(this);this.readToken(this.fullCharCodeAtPos())},z.readToken=function(e){return w(e,this.options.ecmaVersion>=6)||e===92?this.readWord():this.getTokenFromCode(e)},z.fullCharCodeAtPos=function(){var e=this.input.charCodeAt(this.pos);if(e<=55295||e>=56320)return e;var t=this.input.charCodeAt(this.pos+1);return t<=56319||t>=57344?e:(e<<10)+t-56613888},z.skipBlockComment=function(){var e=this.options.onComment&&this.curPosition(),t=this.pos,r=this.input.indexOf("*/",this.pos+=2);if(r===-1&&this.raise(this.pos-2,"Unterminated comment"),this.pos=r+2,this.options.locations)for(var s=void 0,n=t;(s=Z(this.input,n,this.pos))>-1;)++this.curLine,n=this.lineStart=s;this.options.onComment&&this.options.onComment(!0,this.input.slice(t+2,r),t,this.pos,e,this.curPosition())},z.skipLineComment=function(e){for(var t=this.pos,r=this.options.onComment&&this.curPosition(),s=this.input.charCodeAt(this.pos+=e);this.pos<this.input.length&&!j(s);)s=this.input.charCodeAt(++this.pos);this.options.onComment&&this.options.onComment(!1,this.input.slice(t+e,this.pos),t,this.pos,r,this.curPosition())},z.skipSpace=function(){e:for(;this.pos<this.input.length;){var e=this.input.charCodeAt(this.pos);switch(e){case 32:case 160:++this.pos;break;case 13:this.input.charCodeAt(this.pos+1)===10&&++this.pos;case 10:case 8232:case 8233:++this.pos,this.options.locations&&(++this.curLine,this.lineStart=this.pos);break;case 47:switch(this.input.charCodeAt(this.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break e}break;default:if(e>8&&e<14||e>=5760&&ne.test(String.fromCharCode(e)))++this.pos;else break e}}},z.finishToken=function(e,t){this.end=this.pos,this.options.locations&&(this.endLoc=this.curPosition());var r=this.type;this.type=e,this.value=t,this.updateContext(r)},z.readToken_dot=function(){var e=this.input.charCodeAt(this.pos+1);if(e>=48&&e<=57)return this.readNumber(!0);var t=this.input.charCodeAt(this.pos+2);return this.options.ecmaVersion>=6&&e===46&&t===46?(this.pos+=3,this.finishToken(i.ellipsis)):(++this.pos,this.finishToken(i.dot))},z.readToken_slash=function(){var e=this.input.charCodeAt(this.pos+1);return this.exprAllowed?(++this.pos,this.readRegexp()):e===61?this.finishOp(i.assign,2):this.finishOp(i.slash,1)},z.readToken_mult_modulo_exp=function(e){var t=this.input.charCodeAt(this.pos+1),r=1,s=e===42?i.star:i.modulo;return this.options.ecmaVersion>=7&&e===42&&t===42&&(++r,s=i.starstar,t=this.input.charCodeAt(this.pos+2)),t===61?this.finishOp(i.assign,r+1):this.finishOp(s,r)},z.readToken_pipe_amp=function(e){var t=this.input.charCodeAt(this.pos+1);if(t===e){if(this.options.ecmaVersion>=12){var r=this.input.charCodeAt(this.pos+2);if(r===61)return this.finishOp(i.assign,3)}return this.finishOp(e===124?i.logicalOR:i.logicalAND,2)}return t===61?this.finishOp(i.assign,2):this.finishOp(e===124?i.bitwiseOR:i.bitwiseAND,1)},z.readToken_caret=function(){var e=this.input.charCodeAt(this.pos+1);return e===61?this.finishOp(i.assign,2):this.finishOp(i.bitwiseXOR,1)},z.readToken_plus_min=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?t===45&&!this.inModule&&this.input.charCodeAt(this.pos+2)===62&&(this.lastTokEnd===0||S.test(this.input.slice(this.lastTokEnd,this.pos)))?(this.skipLineComment(3),this.skipSpace(),this.nextToken()):this.finishOp(i.incDec,2):t===61?this.finishOp(i.assign,2):this.finishOp(i.plusMin,1)},z.readToken_lt_gt=function(e){var t=this.input.charCodeAt(this.pos+1),r=1;return t===e?(r=e===62&&this.input.charCodeAt(this.pos+2)===62?3:2,this.input.charCodeAt(this.pos+r)===61?this.finishOp(i.assign,r+1):this.finishOp(i.bitShift,r)):t===33&&e===60&&!this.inModule&&this.input.charCodeAt(this.pos+2)===45&&this.input.charCodeAt(this.pos+3)===45?(this.skipLineComment(4),this.skipSpace(),this.nextToken()):(t===61&&(r=2),this.finishOp(i.relational,r))},z.readToken_eq_excl=function(e){var t=this.input.charCodeAt(this.pos+1);return t===61?this.finishOp(i.equality,this.input.charCodeAt(this.pos+2)===61?3:2):e===61&&t===62&&this.options.ecmaVersion>=6?(this.pos+=2,this.finishToken(i.arrow)):this.finishOp(e===61?i.eq:i.prefix,1)},z.readToken_question=function(){var e=this.options.ecmaVersion;if(e>=11){var t=this.input.charCodeAt(this.pos+1);if(t===46){var r=this.input.charCodeAt(this.pos+2);if(r<48||r>57)return this.finishOp(i.questionDot,2)}if(t===63){if(e>=12){var s=this.input.charCodeAt(this.pos+2);if(s===61)return this.finishOp(i.assign,3)}return this.finishOp(i.coalesce,2)}}return this.finishOp(i.question,1)},z.readToken_numberSign=function(){var e=this.options.ecmaVersion,t=35;if(e>=13&&(++this.pos,t=this.fullCharCodeAtPos(),w(t,!0)||t===92))return this.finishToken(i.privateId,this.readWord1());this.raise(this.pos,"Unexpected character '"+C(t)+"'")},z.getTokenFromCode=function(e){switch(e){case 46:return this.readToken_dot();case 40:return++this.pos,this.finishToken(i.parenL);case 41:return++this.pos,this.finishToken(i.parenR);case 59:return++this.pos,this.finishToken(i.semi);case 44:return++this.pos,this.finishToken(i.comma);case 91:return++this.pos,this.finishToken(i.bracketL);case 93:return++this.pos,this.finishToken(i.bracketR);case 123:return++this.pos,this.finishToken(i.braceL);case 125:return++this.pos,this.finishToken(i.braceR);case 58:return++this.pos,this.finishToken(i.colon);case 96:if(this.options.ecmaVersion<6)break;return++this.pos,this.finishToken(i.backQuote);case 48:var t=this.input.charCodeAt(this.pos+1);if(t===120||t===88)return this.readRadixNumber(16);if(this.options.ecmaVersion>=6){if(t===111||t===79)return this.readRadixNumber(8);if(t===98||t===66)return this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(!1);case 34:case 39:return this.readString(e);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(e);case 124:case 38:return this.readToken_pipe_amp(e);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(e);case 60:case 62:return this.readToken_lt_gt(e);case 61:case 33:return this.readToken_eq_excl(e);case 63:return this.readToken_question();case 126:return this.finishOp(i.prefix,1);case 35:return this.readToken_numberSign()}this.raise(this.pos,"Unexpected character '"+C(e)+"'")},z.finishOp=function(e,t){var r=this.input.slice(this.pos,this.pos+t);return this.pos+=t,this.finishToken(e,r)},z.readRegexp=function(){for(var e,t,r=this.pos;;){this.pos>=this.input.length&&this.raise(r,"Unterminated regular expression");var s=this.input.charAt(this.pos);if(S.test(s)&&this.raise(r,"Unterminated regular expression"),e)e=!1;else{if(s==="[")t=!0;else if(s==="]"&&t)t=!1;else if(s==="/"&&!t)break;e=s==="\\"}++this.pos}var n=this.input.slice(r,this.pos);++this.pos;var h=this.pos,c=this.readWord1();this.containsEsc&&this.unexpected(h);var m=this.regexpState||(this.regexpState=new ge(this));m.reset(r,n,c),this.validateRegExpFlags(m),this.validateRegExpPattern(m);var A=null;try{A=new RegExp(n,c)}catch{}return this.finishToken(i.regexp,{pattern:n,flags:c,value:A})},z.readInt=function(e,t,r){for(var s=this.options.ecmaVersion>=12&&t===void 0,n=r&&this.input.charCodeAt(this.pos)===48,h=this.pos,c=0,m=0,A=0,q=t==null?1/0:t;A<q;++A,++this.pos){var W=this.input.charCodeAt(this.pos),re=void 0;if(s&&W===95){n&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed in legacy octal numeric literals"),m===95&&this.raiseRecoverable(this.pos,"Numeric separator must be exactly one underscore"),A===0&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed at the first of digits"),m=W;continue}if(W>=97?re=W-97+10:W>=65?re=W-65+10:W>=48&&W<=57?re=W-48:re=1/0,re>=e)break;m=W,c=c*e+re}return s&&m===95&&this.raiseRecoverable(this.pos-1,"Numeric separator is not allowed at the last of digits"),this.pos===h||t!=null&&this.pos-h!==t?null:c};function hn(e,t){return t?parseInt(e,8):parseFloat(e.replace(/_/g,""))}function Mr(e){return typeof BigInt!="function"?null:BigInt(e.replace(/_/g,""))}z.readRadixNumber=function(e){var t=this.pos;this.pos+=2;var r=this.readInt(e);return r==null&&this.raise(this.start+2,"Expected number in radix "+e),this.options.ecmaVersion>=11&&this.input.charCodeAt(this.pos)===110?(r=Mr(this.input.slice(t,this.pos)),++this.pos):w(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(i.num,r)},z.readNumber=function(e){var t=this.pos;!e&&this.readInt(10,void 0,!0)===null&&this.raise(t,"Invalid number");var r=this.pos-t>=2&&this.input.charCodeAt(t)===48;r&&this.strict&&this.raise(t,"Invalid number");var s=this.input.charCodeAt(this.pos);if(!r&&!e&&this.options.ecmaVersion>=11&&s===110){var n=Mr(this.input.slice(t,this.pos));return++this.pos,w(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(i.num,n)}r&&/[89]/.test(this.input.slice(t,this.pos))&&(r=!1),s===46&&!r&&(++this.pos,this.readInt(10),s=this.input.charCodeAt(this.pos)),(s===69||s===101)&&!r&&(s=this.input.charCodeAt(++this.pos),(s===43||s===45)&&++this.pos,this.readInt(10)===null&&this.raise(t,"Invalid number")),w(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number");var h=hn(this.input.slice(t,this.pos),r);return this.finishToken(i.num,h)},z.readCodePoint=function(){var e=this.input.charCodeAt(this.pos),t;if(e===123){this.options.ecmaVersion<6&&this.unexpected();var r=++this.pos;t=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos),++this.pos,t>1114111&&this.invalidStringToken(r,"Code point out of bounds")}else t=this.readHexChar(4);return t},z.readString=function(e){for(var t="",r=++this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");var s=this.input.charCodeAt(this.pos);if(s===e)break;s===92?(t+=this.input.slice(r,this.pos),t+=this.readEscapedChar(!1),r=this.pos):s===8232||s===8233?(this.options.ecmaVersion<10&&this.raise(this.start,"Unterminated string constant"),++this.pos,this.options.locations&&(this.curLine++,this.lineStart=this.pos)):(j(s)&&this.raise(this.start,"Unterminated string constant"),++this.pos)}return t+=this.input.slice(r,this.pos++),this.finishToken(i.string,t)};var Ur={};z.tryReadTemplateToken=function(){this.inTemplateElement=!0;try{this.readTmplToken()}catch(e){if(e===Ur)this.readInvalidTemplateToken();else throw e}this.inTemplateElement=!1},z.invalidStringToken=function(e,t){if(this.inTemplateElement&&this.options.ecmaVersion>=9)throw Ur;this.raise(e,t)},z.readTmplToken=function(){for(var e="",t=this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated template");var r=this.input.charCodeAt(this.pos);if(r===96||r===36&&this.input.charCodeAt(this.pos+1)===123)return this.pos===this.start&&(this.type===i.template||this.type===i.invalidTemplate)?r===36?(this.pos+=2,this.finishToken(i.dollarBraceL)):(++this.pos,this.finishToken(i.backQuote)):(e+=this.input.slice(t,this.pos),this.finishToken(i.template,e));if(r===92)e+=this.input.slice(t,this.pos),e+=this.readEscapedChar(!0),t=this.pos;else if(j(r)){switch(e+=this.input.slice(t,this.pos),++this.pos,r){case 13:this.input.charCodeAt(this.pos)===10&&++this.pos;case 10:e+=`
`;break;default:e+=String.fromCharCode(r);break}this.options.locations&&(++this.curLine,this.lineStart=this.pos),t=this.pos}else++this.pos}},z.readInvalidTemplateToken=function(){for(;this.pos<this.input.length;this.pos++)switch(this.input[this.pos]){case"\\":++this.pos;break;case"$":if(this.input[this.pos+1]!=="{")break;case"`":return this.finishToken(i.invalidTemplate,this.input.slice(this.start,this.pos))}this.raise(this.start,"Unterminated template")},z.readEscapedChar=function(e){var t=this.input.charCodeAt(++this.pos);switch(++this.pos,t){case 110:return`
`;case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return C(this.readCodePoint());case 116:return"	";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:this.input.charCodeAt(this.pos)===10&&++this.pos;case 10:return this.options.locations&&(this.lineStart=this.pos,++this.curLine),"";case 56:case 57:if(this.strict&&this.invalidStringToken(this.pos-1,"Invalid escape sequence"),e){var r=this.pos-1;return this.invalidStringToken(r,"Invalid escape sequence in template string"),null}default:if(t>=48&&t<=55){var s=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0],n=parseInt(s,8);return n>255&&(s=s.slice(0,-1),n=parseInt(s,8)),this.pos+=s.length-1,t=this.input.charCodeAt(this.pos),(s!=="0"||t===56||t===57)&&(this.strict||e)&&this.invalidStringToken(this.pos-1-s.length,e?"Octal literal in template string":"Octal literal in strict mode"),String.fromCharCode(n)}return j(t)?"":String.fromCharCode(t)}},z.readHexChar=function(e){var t=this.pos,r=this.readInt(16,e);return r===null&&this.invalidStringToken(t,"Bad character escape sequence"),r},z.readWord1=function(){this.containsEsc=!1;for(var e="",t=!0,r=this.pos,s=this.options.ecmaVersion>=6;this.pos<this.input.length;){var n=this.fullCharCodeAtPos();if(G(n,s))this.pos+=n<=65535?1:2;else if(n===92){this.containsEsc=!0,e+=this.input.slice(r,this.pos);var h=this.pos;this.input.charCodeAt(++this.pos)!==117&&this.invalidStringToken(this.pos,"Expecting Unicode escape sequence \\uXXXX"),++this.pos;var c=this.readCodePoint();(t?w:G)(c,s)||this.invalidStringToken(h,"Invalid Unicode escape"),e+=C(c),r=this.pos}else break;t=!1}return e+this.input.slice(r,this.pos)},z.readWord=function(){var e=this.readWord1(),t=i.name;return this.keywords.test(e)&&(t=X[e]),this.finishToken(t,e)};var Wr="8.8.1";Y.acorn={Parser:Y,version:Wr,defaultOptions:fe,Position:H,SourceLocation:te,getLineInfo:ae,Node:Re,TokenType:f,tokTypes:i,keywordTypes:X,TokContext:ue,tokContexts:$,isIdentifierChar:G,isIdentifierStart:w,Token:Qe,isNewLine:j,lineBreak:S,lineBreakG:F,nonASCIIwhitespace:ne};function ln(e,t){return Y.parse(e,t)}function cn(e,t,r){return Y.parseExpressionAt(e,t,r)}function pn(e,t){return Y.tokenizer(e,t)}o.Node=Re,o.Parser=Y,o.Position=H,o.SourceLocation=te,o.TokContext=ue,o.Token=Qe,o.TokenType=f,o.defaultOptions=fe,o.getLineInfo=ae,o.isIdentifierChar=G,o.isIdentifierStart=w,o.isNewLine=j,o.keywordTypes=X,o.lineBreak=S,o.lineBreakG=F,o.nonASCIIwhitespace=ne,o.parse=ln,o.parseExpressionAt=cn,o.tokContexts=$,o.tokTypes=i,o.tokenizer=pn,o.version=Wr,Object.defineProperty(o,"__esModule",{value:!0})})}}),Wh=Q({"node_modules/acorn-jsx/xhtml.js"(a,u){J(),u.exports={quot:'"',amp:"&",apos:"'",lt:"<",gt:">",nbsp:"\xA0",iexcl:"\xA1",cent:"\xA2",pound:"\xA3",curren:"\xA4",yen:"\xA5",brvbar:"\xA6",sect:"\xA7",uml:"\xA8",copy:"\xA9",ordf:"\xAA",laquo:"\xAB",not:"\xAC",shy:"\xAD",reg:"\xAE",macr:"\xAF",deg:"\xB0",plusmn:"\xB1",sup2:"\xB2",sup3:"\xB3",acute:"\xB4",micro:"\xB5",para:"\xB6",middot:"\xB7",cedil:"\xB8",sup1:"\xB9",ordm:"\xBA",raquo:"\xBB",frac14:"\xBC",frac12:"\xBD",frac34:"\xBE",iquest:"\xBF",Agrave:"\xC0",Aacute:"\xC1",Acirc:"\xC2",Atilde:"\xC3",Auml:"\xC4",Aring:"\xC5",AElig:"\xC6",Ccedil:"\xC7",Egrave:"\xC8",Eacute:"\xC9",Ecirc:"\xCA",Euml:"\xCB",Igrave:"\xCC",Iacute:"\xCD",Icirc:"\xCE",Iuml:"\xCF",ETH:"\xD0",Ntilde:"\xD1",Ograve:"\xD2",Oacute:"\xD3",Ocirc:"\xD4",Otilde:"\xD5",Ouml:"\xD6",times:"\xD7",Oslash:"\xD8",Ugrave:"\xD9",Uacute:"\xDA",Ucirc:"\xDB",Uuml:"\xDC",Yacute:"\xDD",THORN:"\xDE",szlig:"\xDF",agrave:"\xE0",aacute:"\xE1",acirc:"\xE2",atilde:"\xE3",auml:"\xE4",aring:"\xE5",aelig:"\xE6",ccedil:"\xE7",egrave:"\xE8",eacute:"\xE9",ecirc:"\xEA",euml:"\xEB",igrave:"\xEC",iacute:"\xED",icirc:"\xEE",iuml:"\xEF",eth:"\xF0",ntilde:"\xF1",ograve:"\xF2",oacute:"\xF3",ocirc:"\xF4",otilde:"\xF5",ouml:"\xF6",divide:"\xF7",oslash:"\xF8",ugrave:"\xF9",uacute:"\xFA",ucirc:"\xFB",uuml:"\xFC",yacute:"\xFD",thorn:"\xFE",yuml:"\xFF",OElig:"\u0152",oelig:"\u0153",Scaron:"\u0160",scaron:"\u0161",Yuml:"\u0178",fnof:"\u0192",circ:"\u02C6",tilde:"\u02DC",Alpha:"\u0391",Beta:"\u0392",Gamma:"\u0393",Delta:"\u0394",Epsilon:"\u0395",Zeta:"\u0396",Eta:"\u0397",Theta:"\u0398",Iota:"\u0399",Kappa:"\u039A",Lambda:"\u039B",Mu:"\u039C",Nu:"\u039D",Xi:"\u039E",Omicron:"\u039F",Pi:"\u03A0",Rho:"\u03A1",Sigma:"\u03A3",Tau:"\u03A4",Upsilon:"\u03A5",Phi:"\u03A6",Chi:"\u03A7",Psi:"\u03A8",Omega:"\u03A9",alpha:"\u03B1",beta:"\u03B2",gamma:"\u03B3",delta:"\u03B4",epsilon:"\u03B5",zeta:"\u03B6",eta:"\u03B7",theta:"\u03B8",iota:"\u03B9",kappa:"\u03BA",lambda:"\u03BB",mu:"\u03BC",nu:"\u03BD",xi:"\u03BE",omicron:"\u03BF",pi:"\u03C0",rho:"\u03C1",sigmaf:"\u03C2",sigma:"\u03C3",tau:"\u03C4",upsilon:"\u03C5",phi:"\u03C6",chi:"\u03C7",psi:"\u03C8",omega:"\u03C9",thetasym:"\u03D1",upsih:"\u03D2",piv:"\u03D6",ensp:"\u2002",emsp:"\u2003",thinsp:"\u2009",zwnj:"\u200C",zwj:"\u200D",lrm:"\u200E",rlm:"\u200F",ndash:"\u2013",mdash:"\u2014",lsquo:"\u2018",rsquo:"\u2019",sbquo:"\u201A",ldquo:"\u201C",rdquo:"\u201D",bdquo:"\u201E",dagger:"\u2020",Dagger:"\u2021",bull:"\u2022",hellip:"\u2026",permil:"\u2030",prime:"\u2032",Prime:"\u2033",lsaquo:"\u2039",rsaquo:"\u203A",oline:"\u203E",frasl:"\u2044",euro:"\u20AC",image:"\u2111",weierp:"\u2118",real:"\u211C",trade:"\u2122",alefsym:"\u2135",larr:"\u2190",uarr:"\u2191",rarr:"\u2192",darr:"\u2193",harr:"\u2194",crarr:"\u21B5",lArr:"\u21D0",uArr:"\u21D1",rArr:"\u21D2",dArr:"\u21D3",hArr:"\u21D4",forall:"\u2200",part:"\u2202",exist:"\u2203",empty:"\u2205",nabla:"\u2207",isin:"\u2208",notin:"\u2209",ni:"\u220B",prod:"\u220F",sum:"\u2211",minus:"\u2212",lowast:"\u2217",radic:"\u221A",prop:"\u221D",infin:"\u221E",ang:"\u2220",and:"\u2227",or:"\u2228",cap:"\u2229",cup:"\u222A",int:"\u222B",there4:"\u2234",sim:"\u223C",cong:"\u2245",asymp:"\u2248",ne:"\u2260",equiv:"\u2261",le:"\u2264",ge:"\u2265",sub:"\u2282",sup:"\u2283",nsub:"\u2284",sube:"\u2286",supe:"\u2287",oplus:"\u2295",otimes:"\u2297",perp:"\u22A5",sdot:"\u22C5",lceil:"\u2308",rceil:"\u2309",lfloor:"\u230A",rfloor:"\u230B",lang:"\u2329",rang:"\u232A",loz:"\u25CA",spades:"\u2660",clubs:"\u2663",hearts:"\u2665",diams:"\u2666"}}}),za=Q({"node_modules/acorn-jsx/index.js"(a,u){"use strict";J();var o=Wh(),l=/^[\da-fA-F]+$/,v=/^\d+$/,b=new WeakMap;function y(x){x=x.Parser.acorn||x;let R=b.get(x);if(!R){let U=x.tokTypes,D=x.TokContext,g=x.TokenType,w=new D("<tag",!1),G=new D("</tag",!1),f=new D("<tag>...</tag>",!0,!0),B={tc_oTag:w,tc_cTag:G,tc_expr:f},V={jsxName:new g("jsxName"),jsxText:new g("jsxText",{beforeExpr:!0}),jsxTagStart:new g("jsxTagStart",{startsExpr:!0}),jsxTagEnd:new g("jsxTagEnd")};V.jsxTagStart.updateContext=function(){this.context.push(f),this.context.push(w),this.exprAllowed=!1},V.jsxTagEnd.updateContext=function(k){let X=this.context.pop();X===w&&k===U.slash||X===G?(this.context.pop(),this.exprAllowed=this.curContext()===f):this.exprAllowed=!0},R={tokContexts:B,tokTypes:V},b.set(x,R)}return R}function I(x){if(!x)return x;if(x.type==="JSXIdentifier")return x.name;if(x.type==="JSXNamespacedName")return x.namespace.name+":"+x.name.name;if(x.type==="JSXMemberExpression")return I(x.object)+"."+I(x.property)}u.exports=function(x){return x=x||{},function(R){return T({allowNamespaces:x.allowNamespaces!==!1,allowNamespacedObjects:!!x.allowNamespacedObjects},R)}},Object.defineProperty(u.exports,"tokTypes",{get:function(){return y(ft()).tokTypes},configurable:!0,enumerable:!0});function T(x,R){let U=R.acorn||ft(),D=y(U),g=U.tokTypes,w=D.tokTypes,G=U.tokContexts,f=D.tokContexts.tc_oTag,B=D.tokContexts.tc_cTag,V=D.tokContexts.tc_expr,k=U.isNewLine,X=U.isIdentifierStart,O=U.isIdentifierChar;return class extends R{static get acornJsx(){return D}jsx_readToken(){let i="",S=this.pos;for(;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated JSX contents");let F=this.input.charCodeAt(this.pos);switch(F){case 60:case 123:return this.pos===this.start?F===60&&this.exprAllowed?(++this.pos,this.finishToken(w.jsxTagStart)):this.getTokenFromCode(F):(i+=this.input.slice(S,this.pos),this.finishToken(w.jsxText,i));case 38:i+=this.input.slice(S,this.pos),i+=this.jsx_readEntity(),S=this.pos;break;case 62:case 125:this.raise(this.pos,"Unexpected token `"+this.input[this.pos]+"`. Did you mean `"+(F===62?"&gt;":"&rbrace;")+'` or `{"'+this.input[this.pos]+'"}`?');default:k(F)?(i+=this.input.slice(S,this.pos),i+=this.jsx_readNewLine(!0),S=this.pos):++this.pos}}}jsx_readNewLine(i){let S=this.input.charCodeAt(this.pos),F;return++this.pos,S===13&&this.input.charCodeAt(this.pos)===10?(++this.pos,F=i?`
`:`\r
`):F=String.fromCharCode(S),this.options.locations&&(++this.curLine,this.lineStart=this.pos),F}jsx_readString(i){let S="",F=++this.pos;for(;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");let j=this.input.charCodeAt(this.pos);if(j===i)break;j===38?(S+=this.input.slice(F,this.pos),S+=this.jsx_readEntity(),F=this.pos):k(j)?(S+=this.input.slice(F,this.pos),S+=this.jsx_readNewLine(!1),F=this.pos):++this.pos}return S+=this.input.slice(F,this.pos++),this.finishToken(g.string,S)}jsx_readEntity(){let i="",S=0,F,j=this.input[this.pos];j!=="&"&&this.raise(this.pos,"Entity must start with an ampersand");let Z=++this.pos;for(;this.pos<this.input.length&&S++<10;){if(j=this.input[this.pos++],j===";"){i[0]==="#"?i[1]==="x"?(i=i.substr(2),l.test(i)&&(F=String.fromCharCode(parseInt(i,16)))):(i=i.substr(1),v.test(i)&&(F=String.fromCharCode(parseInt(i,10)))):F=o[i];break}i+=j}return F||(this.pos=Z,"&")}jsx_readWord(){let i,S=this.pos;do i=this.input.charCodeAt(++this.pos);while(O(i)||i===45);return this.finishToken(w.jsxName,this.input.slice(S,this.pos))}jsx_parseIdentifier(){let i=this.startNode();return this.type===w.jsxName?i.name=this.value:this.type.keyword?i.name=this.type.keyword:this.unexpected(),this.next(),this.finishNode(i,"JSXIdentifier")}jsx_parseNamespacedName(){let i=this.start,S=this.startLoc,F=this.jsx_parseIdentifier();if(!x.allowNamespaces||!this.eat(g.colon))return F;var j=this.startNodeAt(i,S);return j.namespace=F,j.name=this.jsx_parseIdentifier(),this.finishNode(j,"JSXNamespacedName")}jsx_parseElementName(){if(this.type===w.jsxTagEnd)return"";let i=this.start,S=this.startLoc,F=this.jsx_parseNamespacedName();for(this.type===g.dot&&F.type==="JSXNamespacedName"&&!x.allowNamespacedObjects&&this.unexpected();this.eat(g.dot);){let j=this.startNodeAt(i,S);j.object=F,j.property=this.jsx_parseIdentifier(),F=this.finishNode(j,"JSXMemberExpression")}return F}jsx_parseAttributeValue(){switch(this.type){case g.braceL:let i=this.jsx_parseExpressionContainer();return i.expression.type==="JSXEmptyExpression"&&this.raise(i.start,"JSX attributes must only be assigned a non-empty expression"),i;case w.jsxTagStart:case g.string:return this.parseExprAtom();default:this.raise(this.start,"JSX value should be either an expression or a quoted JSX text")}}jsx_parseEmptyExpression(){let i=this.startNodeAt(this.lastTokEnd,this.lastTokEndLoc);return this.finishNodeAt(i,"JSXEmptyExpression",this.start,this.startLoc)}jsx_parseExpressionContainer(){let i=this.startNode();return this.next(),i.expression=this.type===g.braceR?this.jsx_parseEmptyExpression():this.parseExpression(),this.expect(g.braceR),this.finishNode(i,"JSXExpressionContainer")}jsx_parseAttribute(){let i=this.startNode();return this.eat(g.braceL)?(this.expect(g.ellipsis),i.argument=this.parseMaybeAssign(),this.expect(g.braceR),this.finishNode(i,"JSXSpreadAttribute")):(i.name=this.jsx_parseNamespacedName(),i.value=this.eat(g.eq)?this.jsx_parseAttributeValue():null,this.finishNode(i,"JSXAttribute"))}jsx_parseOpeningElementAt(i,S){let F=this.startNodeAt(i,S);F.attributes=[];let j=this.jsx_parseElementName();for(j&&(F.name=j);this.type!==g.slash&&this.type!==w.jsxTagEnd;)F.attributes.push(this.jsx_parseAttribute());return F.selfClosing=this.eat(g.slash),this.expect(w.jsxTagEnd),this.finishNode(F,j?"JSXOpeningElement":"JSXOpeningFragment")}jsx_parseClosingElementAt(i,S){let F=this.startNodeAt(i,S),j=this.jsx_parseElementName();return j&&(F.name=j),this.expect(w.jsxTagEnd),this.finishNode(F,j?"JSXClosingElement":"JSXClosingFragment")}jsx_parseElementAt(i,S){let F=this.startNodeAt(i,S),j=[],Z=this.jsx_parseOpeningElementAt(i,S),ne=null;if(!Z.selfClosing){e:for(;;)switch(this.type){case w.jsxTagStart:if(i=this.start,S=this.startLoc,this.next(),this.eat(g.slash)){ne=this.jsx_parseClosingElementAt(i,S);break e}j.push(this.jsx_parseElementAt(i,S));break;case w.jsxText:j.push(this.parseExprAtom());break;case g.braceL:j.push(this.jsx_parseExpressionContainer());break;default:this.unexpected()}I(ne.name)!==I(Z.name)&&this.raise(ne.start,"Expected corresponding JSX closing tag for <"+I(Z.name)+">")}let ee=Z.name?"Element":"Fragment";return F["opening"+ee]=Z,F["closing"+ee]=ne,F.children=j,this.type===g.relational&&this.value==="<"&&this.raise(this.start,"Adjacent JSX elements must be wrapped in an enclosing tag"),this.finishNode(F,"JSX"+ee)}jsx_parseText(){let i=this.parseLiteral(this.value);return i.type="JSXText",i}jsx_parseElement(){let i=this.start,S=this.startLoc;return this.next(),this.jsx_parseElementAt(i,S)}parseExprAtom(i){return this.type===w.jsxText?this.jsx_parseText():this.type===w.jsxTagStart?this.jsx_parseElement():super.parseExprAtom(i)}readToken(i){let S=this.curContext();if(S===V)return this.jsx_readToken();if(S===f||S===B){if(X(i))return this.jsx_readWord();if(i==62)return++this.pos,this.finishToken(w.jsxTagEnd);if((i===34||i===39)&&S==f)return this.jsx_readString(i)}return i===60&&this.exprAllowed&&this.input.charCodeAt(this.pos+1)!==33?(++this.pos,this.finishToken(w.jsxTagStart)):super.readToken(i)}updateContext(i){if(this.type==g.braceL){var S=this.curContext();S==f?this.context.push(G.b_expr):S==V?this.context.push(G.b_tmpl):super.updateContext(i),this.exprAllowed=!0}else if(this.type===g.slash&&i===w.jsxTagStart)this.context.length-=2,this.context.push(B),this.exprAllowed=!1;else return super.updateContext(i)}}}}}),zh=Q({"src/language-js/parse/acorn.js"(a,u){"use strict";J();var o=dr(),l=ka(),v=Ua(),b=Wa(),y={ecmaVersion:"latest",sourceType:"module",allowReserved:!0,allowReturnOutsideFunction:!0,allowImportExportEverywhere:!0,allowAwaitOutsideFunction:!0,allowSuperOutsideMethod:!0,allowHashBang:!0,locations:!0,ranges:!0};function I(D){let{message:g,loc:w}=D;if(!w)return D;let{line:G,column:f}=w;return o(g.replace(/ \(\d+:\d+\)$/,""),{start:{line:G,column:f+1}})}var T,x=()=>{if(!T){let{Parser:D}=ft(),g=za();T=D.extend(g())}return T};function R(D,g){let w=x(),G=[],f=[],B=w.parse(D,Object.assign(Object.assign({},y),{},{sourceType:g,onComment:G,onToken:f}));return B.comments=G,B.tokens=f,B}function U(D,g){let w=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},{result:G,error:f}=l(()=>R(D,"module"),()=>R(D,"script"));if(!G)throw I(f);return w.originalText=D,b(G,w)}u.exports=v(U)}}),Gh=Q({"src/language-js/parse/utils/replace-hashbang.js"(a,u){"use strict";J();function o(l){return l.charAt(0)==="#"&&l.charAt(1)==="!"?"//"+l.slice(2):l}u.exports=o}}),Hh=Q({"node_modules/espree/dist/espree.cjs"(a){"use strict";J(),Object.defineProperty(a,"__esModule",{value:!0});var u=ft(),o=za(),l;function v(p){return p&&typeof p=="object"&&"default"in p?p:{default:p}}function b(p){if(p&&p.__esModule)return p;var P=Object.create(null);return p&&Object.keys(p).forEach(function(_){if(_!=="default"){var d=Object.getOwnPropertyDescriptor(p,_);Object.defineProperty(P,_,d.get?d:{enumerable:!0,get:function(){return p[_]}})}}),P.default=p,Object.freeze(P)}var y=b(u),I=v(o),T=b(l),x={Boolean:"Boolean",EOF:"<end>",Identifier:"Identifier",PrivateIdentifier:"PrivateIdentifier",Keyword:"Keyword",Null:"Null",Numeric:"Numeric",Punctuator:"Punctuator",String:"String",RegularExpression:"RegularExpression",Template:"Template",JSXIdentifier:"JSXIdentifier",JSXText:"JSXText"};function R(p,P){let _=p[0],d=p[p.length-1],C={type:x.Template,value:P.slice(_.start,d.end)};return _.loc&&(C.loc={start:_.loc.start,end:d.loc.end}),_.range&&(C.start=_.range[0],C.end=d.range[1],C.range=[C.start,C.end]),C}function U(p,P){this._acornTokTypes=p,this._tokens=[],this._curlyBrace=null,this._code=P}U.prototype={constructor:U,translate(p,P){let _=p.type,d=this._acornTokTypes;if(_===d.name)p.type=x.Identifier,p.value==="static"&&(p.type=x.Keyword),P.ecmaVersion>5&&(p.value==="yield"||p.value==="let")&&(p.type=x.Keyword);else if(_===d.privateId)p.type=x.PrivateIdentifier;else if(_===d.semi||_===d.comma||_===d.parenL||_===d.parenR||_===d.braceL||_===d.braceR||_===d.dot||_===d.bracketL||_===d.colon||_===d.question||_===d.bracketR||_===d.ellipsis||_===d.arrow||_===d.jsxTagStart||_===d.incDec||_===d.starstar||_===d.jsxTagEnd||_===d.prefix||_===d.questionDot||_.binop&&!_.keyword||_.isAssign)p.type=x.Punctuator,p.value=this._code.slice(p.start,p.end);else if(_===d.jsxName)p.type=x.JSXIdentifier;else if(_.label==="jsxText"||_===d.jsxAttrValueToken)p.type=x.JSXText;else if(_.keyword)_.keyword==="true"||_.keyword==="false"?p.type=x.Boolean:_.keyword==="null"?p.type=x.Null:p.type=x.Keyword;else if(_===d.num)p.type=x.Numeric,p.value=this._code.slice(p.start,p.end);else if(_===d.string)P.jsxAttrValueToken?(P.jsxAttrValueToken=!1,p.type=x.JSXText):p.type=x.String,p.value=this._code.slice(p.start,p.end);else if(_===d.regexp){p.type=x.RegularExpression;let C=p.value;p.regex={flags:C.flags,pattern:C.pattern},p.value=`/${C.pattern}/${C.flags}`}return p},onToken(p,P){let _=this,d=this._acornTokTypes,C=P.tokens,K=this._tokens;function H(){C.push(R(_._tokens,_._code)),_._tokens=[]}if(p.type===d.eof){this._curlyBrace&&C.push(this.translate(this._curlyBrace,P));return}if(p.type===d.backQuote){this._curlyBrace&&(C.push(this.translate(this._curlyBrace,P)),this._curlyBrace=null),K.push(p),K.length>1&&H();return}if(p.type===d.dollarBraceL){K.push(p),H();return}if(p.type===d.braceR){this._curlyBrace&&C.push(this.translate(this._curlyBrace,P)),this._curlyBrace=p;return}if(p.type===d.template||p.type===d.invalidTemplate){this._curlyBrace&&(K.push(this._curlyBrace),this._curlyBrace=null),K.push(p);return}this._curlyBrace&&(C.push(this.translate(this._curlyBrace,P)),this._curlyBrace=null),C.push(this.translate(p,P))}};var D=[3,5,6,7,8,9,10,11,12,13,14];function g(){return D[D.length-1]}function w(){return[...D]}function G(){let p=arguments.length>0&&arguments[0]!==void 0?arguments[0]:5,P=p==="latest"?g():p;if(typeof P!="number")throw new Error(`ecmaVersion must be a number or "latest". Received value of type ${typeof p} instead.`);if(P>=2015&&(P-=2009),!D.includes(P))throw new Error("Invalid ecmaVersion.");return P}function f(){let p=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"script";if(p==="script"||p==="module")return p;if(p==="commonjs")return"script";throw new Error("Invalid sourceType.")}function B(p){let P=G(p.ecmaVersion),_=f(p.sourceType),d=p.range===!0,C=p.loc===!0;if(P!==3&&p.allowReserved)throw new Error("`allowReserved` is only supported when ecmaVersion is 3");if(typeof p.allowReserved<"u"&&typeof p.allowReserved!="boolean")throw new Error("`allowReserved`, when present, must be `true` or `false`");let K=P===3?p.allowReserved||"never":!1,H=p.ecmaFeatures||{},te=p.sourceType==="commonjs"||Boolean(H.globalReturn);if(_==="module"&&P<6)throw new Error("sourceType 'module' is not supported when ecmaVersion < 2015. Consider adding `{ ecmaVersion: 2015 }` to the parser options.");return Object.assign({},p,{ecmaVersion:P,sourceType:_,ranges:d,locations:C,allowReserved:K,allowReturnOutsideFunction:te})}var V=Symbol("espree's internal state"),k=Symbol("espree's esprimaFinishNode");function X(p,P,_,d,C,K,H){let te;p?te="Block":H.slice(_,_+2)==="#!"?te="Hashbang":te="Line";let ae={type:te,value:P};return typeof _=="number"&&(ae.start=_,ae.end=d,ae.range=[_,d]),typeof C=="object"&&(ae.loc={start:C,end:K}),ae}var O=()=>p=>{let P=Object.assign({},p.acorn.tokTypes);return p.acornJsx&&Object.assign(P,p.acornJsx.tokTypes),class extends p{constructor(d,C){(typeof d!="object"||d===null)&&(d={}),typeof C!="string"&&!(C instanceof String)&&(C=String(C));let K=d.sourceType,H=B(d),te=H.ecmaFeatures||{},ae=H.tokens===!0?new U(P,C):null,fe={originalSourceType:K||H.sourceType,tokens:ae?[]:null,comments:H.comment===!0?[]:null,impliedStrict:te.impliedStrict===!0&&H.ecmaVersion>=5,ecmaVersion:H.ecmaVersion,jsxAttrValueToken:!1,lastToken:null,templateElements:[]};super({ecmaVersion:H.ecmaVersion,sourceType:H.sourceType,ranges:H.ranges,locations:H.locations,allowReserved:H.allowReserved,allowReturnOutsideFunction:H.allowReturnOutsideFunction,onToken:Ae=>{ae&&ae.onToken(Ae,fe),Ae.type!==P.eof&&(fe.lastToken=Ae)},onComment:(Ae,dt,mt,_e,Ce,Oe)=>{if(fe.comments){let ze=X(Ae,dt,mt,_e,Ce,Oe,C);fe.comments.push(ze)}}},C),this[V]=fe}tokenize(){do this.next();while(this.type!==P.eof);this.next();let d=this[V],C=d.tokens;return d.comments&&(C.comments=d.comments),C}finishNode(){let d=super.finishNode(...arguments);return this[k](d)}finishNodeAt(){let d=super.finishNodeAt(...arguments);return this[k](d)}parse(){let d=this[V],C=super.parse();if(C.sourceType=d.originalSourceType,d.comments&&(C.comments=d.comments),d.tokens&&(C.tokens=d.tokens),C.body.length){let[K]=C.body;C.range&&(C.range[0]=K.range[0]),C.loc&&(C.loc.start=K.loc.start),C.start=K.start}return d.lastToken&&(C.range&&(C.range[1]=d.lastToken.range[1]),C.loc&&(C.loc.end=d.lastToken.loc.end),C.end=d.lastToken.end),this[V].templateElements.forEach(K=>{let te=K.tail?1:2;K.start+=-1,K.end+=te,K.range&&(K.range[0]+=-1,K.range[1]+=te),K.loc&&(K.loc.start.column+=-1,K.loc.end.column+=te)}),C}parseTopLevel(d){return this[V].impliedStrict&&(this.strict=!0),super.parseTopLevel(d)}raise(d,C){let K=p.acorn.getLineInfo(this.input,d),H=new SyntaxError(C);throw H.index=d,H.lineNumber=K.line,H.column=K.column+1,H}raiseRecoverable(d,C){this.raise(d,C)}unexpected(d){let C="Unexpected token";if(d!=null){if(this.pos=d,this.options.locations)for(;this.pos<this.lineStart;)this.lineStart=this.input.lastIndexOf(`
`,this.lineStart-2)+1,--this.curLine;this.nextToken()}this.end>this.start&&(C+=` ${this.input.slice(this.start,this.end)}`),this.raise(this.start,C)}jsx_readString(d){let C=super.jsx_readString(d);return this.type===P.string&&(this[V].jsxAttrValueToken=!0),C}[k](d){return d.type==="TemplateElement"&&this[V].templateElements.push(d),d.type.includes("Function")&&!d.generator&&(d.generator=!1),d}}},i="9.4.1",S={_regular:null,_jsx:null,get regular(){return this._regular===null&&(this._regular=y.Parser.extend(O())),this._regular},get jsx(){return this._jsx===null&&(this._jsx=y.Parser.extend(I.default(),O())),this._jsx},get(p){return Boolean(p&&p.ecmaFeatures&&p.ecmaFeatures.jsx)?this.jsx:this.regular}};function F(p,P){let _=S.get(P);return(!P||P.tokens!==!0)&&(P=Object.assign({},P,{tokens:!0})),new _(P,p).tokenize()}function j(p,P){let _=S.get(P);return new _(P,p).parse()}var Z=i,ne=function(){return T.KEYS}(),ee=void 0,ie=g(),Ne=w();a.Syntax=ee,a.VisitorKeys=ne,a.latestEcmaVersion=ie,a.parse=j,a.supportedEcmaVersions=Ne,a.tokenize=F,a.version=Z}}),Kh=Q({"src/language-js/parse/espree.js"(a,u){"use strict";J();var o=dr(),l=ka(),v=Ua(),b=Gh(),y=Wa(),I={ecmaVersion:"latest",range:!0,loc:!0,comment:!0,tokens:!0,sourceType:"module",ecmaFeatures:{jsx:!0,globalReturn:!0,impliedStrict:!1}};function T(R){let{message:U,lineNumber:D,column:g}=R;return typeof D!="number"?R:o(U,{start:{line:D,column:g}})}function x(R,U){let D=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},{parse:g}=Hh(),w=b(R),{result:G,error:f}=l(()=>g(w,Object.assign(Object.assign({},I),{},{sourceType:"module"})),()=>g(w,Object.assign(Object.assign({},I),{},{sourceType:"script"})));if(!G)throw T(f);return D.originalText=R,y(G,D)}u.exports=v(x)}}),Xh=Q({"src/language-js/parse/acorn-and-espree.js"(a,u){J();var o=zh(),l=Kh();u.exports={parsers:{acorn:o,espree:l}}}}),mc=Xh();export{mc as default};

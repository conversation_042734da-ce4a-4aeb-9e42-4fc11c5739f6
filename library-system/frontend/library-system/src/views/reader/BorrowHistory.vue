<template>
  <div class="borrowing-history">
    <!-- Header Section -->
    <div class="header-section">
      <h2>Borrowing History</h2>
      <p>View your complete borrowing history and past transactions</p>
    </div>

    <!-- Summary Statistics -->
    <div class="summary-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="Total Books Borrowed"
              :value="historyStats.totalBorrowed"
              :value-style="{ color: '#1890ff' }"
            >
              <template #suffix>
                <BookOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="Books Returned"
              :value="historyStats.totalReturned"
              :value-style="{ color: '#52c41a' }"
            >
              <template #suffix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="Total Fines Paid"
              :value="historyStats.totalFinesPaid"
              :precision="2"
              prefix="¥"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #suffix>
                <DollarOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="Average Days Borrowed"
              :value="historyStats.avgDaysBorrowed"
              :precision="1"
              :value-style="{ color: '#722ed1' }"
            >
              <template #suffix>
                <CalendarOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- Search and Filter Section -->
    <div class="filter-section">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="Search by book title or author"
            @change="handleSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="6">
          <a-date-picker
            v-model:value="searchForm.startDate"
            placeholder="Start date"
            @change="handleSearch"
            style="width: 100%"
          />
        </a-col>
        <a-col :span="6">
          <a-date-picker
            v-model:value="searchForm.endDate"
            placeholder="End date"
            @change="handleSearch"
            style="width: 100%"
          />
        </a-col>
        <a-col :span="4">
          <a-button @click="resetFilters">
            <ReloadOutlined />
            Reset
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- History Table -->
    <div class="history-section">
      <div v-if="loading" class="loading-container">
        <a-spin size="large" />
      </div>

      <div v-else-if="historyRecords.length === 0" class="empty-state">
        <a-empty description="No borrowing history found">
          <a-button type="primary" @click="navigateToSearch">
            Start Borrowing Books
          </a-button>
        </a-empty>
      </div>

      <a-table
        v-else
        :columns="historyColumns"
        :data-source="historyRecords"
        :loading="loading"
        :pagination="paginationConfig"
        @change="handleTableChange"
        row-key="id"
      >
        <!-- Book Column -->
        <template #book="{ record }">
          <div class="book-info">
            <div class="book-title">{{ record.bookTitle }}</div>
            <div class="book-author">{{ record.bookAuthor }}</div>
            <div class="book-isbn">ISBN: {{ record.isbn }}</div>
          </div>
        </template>

        <!-- Dates Column -->
        <template #dates="{ record }">
          <div class="dates-info">
            <div><strong>Borrowed:</strong> {{ formatDate(record.borrowDate) }}</div>
            <div><strong>Due:</strong> {{ formatDate(record.dueDate) }}</div>
            <div><strong>Returned:</strong> {{ formatDate(record.returnDate) }}</div>
            <div v-if="record.renewalCount > 0" class="renewal-info">
              <a-tag color="orange" size="small">
                Renewed {{ record.renewalCount }} time(s)
              </a-tag>
            </div>
          </div>
        </template>

        <!-- Status Column -->
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ record.status }}
          </a-tag>
        </template>

        <!-- Fine Column -->
        <template #fine="{ record }">
          <span v-if="record.fineAmount > 0" class="fine-amount">
            ¥{{ record.fineAmount.toFixed(2) }}
          </span>
          <span v-else class="no-fine">
            ¥0.00
          </span>
        </template>

        <!-- Duration Column -->
        <template #duration="{ record }">
          <span>{{ calculateDuration(record.borrowDate, record.returnDate) }} days</span>
        </template>

        <!-- Actions Column -->
        <template #actions="{ record }">
          <a-button
            type="link"
            size="small"
            @click="viewDetails(record)"
          >
            <EyeOutlined />
            Details
          </a-button>
        </template>
      </a-table>
    </div>

    <!-- Details Modal -->
    <a-modal
      v-model:open="detailsModalVisible"
      title="Borrowing Record Details"
      :footer="null"
      width="600px"
    >
      <div v-if="selectedRecord" class="record-details">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="Record ID">
            {{ selectedRecord.id }}
          </a-descriptions-item>
          <a-descriptions-item label="Status">
            <a-tag :color="getStatusColor(selectedRecord.status)">
              {{ selectedRecord.status }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="Book Title" :span="2">
            {{ selectedRecord.bookTitle }}
          </a-descriptions-item>
          <a-descriptions-item label="Author">
            {{ selectedRecord.bookAuthor }}
          </a-descriptions-item>
          <a-descriptions-item label="ISBN">
            {{ selectedRecord.isbn }}
          </a-descriptions-item>
          <a-descriptions-item label="Borrow Date">
            {{ formatDate(selectedRecord.borrowDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="Due Date">
            {{ formatDate(selectedRecord.dueDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="Return Date">
            {{ formatDate(selectedRecord.returnDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="Duration">
            {{ calculateDuration(selectedRecord.borrowDate, selectedRecord.returnDate) }} days
          </a-descriptions-item>
          <a-descriptions-item label="Renewal Count">
            {{ selectedRecord.renewalCount }}
          </a-descriptions-item>
          <a-descriptions-item label="Fine Amount">
            <span :style="{ color: selectedRecord.fineAmount > 0 ? '#ff4d4f' : '#52c41a' }">
              ¥{{ selectedRecord.fineAmount.toFixed(2) }}
            </span>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  BookOutlined,
  CheckCircleOutlined,
  DollarOutlined,
  CalendarOutlined,
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined
} from '@ant-design/icons-vue'
import {
  getBorrowRecordsUsingGet
} from '@/api/borrowController'
import { useAuthStore } from '@/stores/auth'
import dayjs from 'dayjs'

const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const detailsModalVisible = ref(false)
const selectedRecord = ref<any>(null)

const historyRecords = ref<any[]>([])
const historyStats = reactive({
  totalBorrowed: 0,
  totalReturned: 0,
  totalFinesPaid: 0,
  avgDaysBorrowed: 0
})

// Search form
const searchForm = reactive({
  keyword: '',
  startDate: null,
  endDate: null
})

// Pagination
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// Table columns
const historyColumns = [
  {
    title: 'Book',
    key: 'book',
    slots: { customRender: 'book' },
    width: 250
  },
  {
    title: 'Dates',
    key: 'dates',
    slots: { customRender: 'dates' },
    width: 200
  },
  {
    title: 'Status',
    key: 'status',
    slots: { customRender: 'status' },
    width: 100
  },
  {
    title: 'Duration',
    key: 'duration',
    slots: { customRender: 'duration' },
    width: 100
  },
  {
    title: 'Fine',
    key: 'fine',
    slots: { customRender: 'fine' },
    width: 100
  },
  {
    title: 'Actions',
    key: 'actions',
    slots: { customRender: 'actions' },
    width: 100
  }
]

// Computed
const paginationConfig = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: number[]) =>
    `${range[0]}-${range[1]} of ${total} records`
}))

// Methods
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('MMM DD, YYYY')
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'RETURNED': return 'green'
    case 'OVERDUE': return 'red'
    case 'RENEWED': return 'orange'
    default: return 'default'
  }
}

const calculateDuration = (borrowDate: string, returnDate: string) => {
  const borrow = dayjs(borrowDate)
  const returned = dayjs(returnDate)
  return returned.diff(borrow, 'day')
}

const handleSearch = () => {
  pagination.current = 1
  fetchHistory()
}

const resetFilters = () => {
  searchForm.keyword = ''
  searchForm.startDate = null
  searchForm.endDate = null
  pagination.current = 1
  fetchHistory()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchHistory()
}

const viewDetails = (record: any) => {
  selectedRecord.value = record
  detailsModalVisible.value = true
}

const navigateToSearch = () => {
  router.push('/reader/search')
}

const fetchHistory = async () => {
  if (!authStore.user?.userId) {
    message.error('请先登录')
    return
  }

  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      readerId: authStore.user.userId,
      keyword: searchForm.keyword || undefined,
      startDate: searchForm.startDate ? dayjs(searchForm.startDate).format('YYYY-MM-DD') : undefined,
      endDate: searchForm.endDate ? dayjs(searchForm.endDate).format('YYYY-MM-DD') : undefined
    }

    const response = await getBorrowRecordsUsingGet(params)
    if (response.success && response.data) {
      historyRecords.value = response.data.records || []
      pagination.current = response.data.current || 1
      pagination.total = response.data.total || 0

      // Calculate statistics
      calculateStats(historyRecords.value)
    }
  } catch (error) {
    console.error('Fetch history error:', error)
    message.error('获取借阅历史失败')
  } finally {
    loading.value = false
  }
}

const calculateStats = (records: any[]) => {
  historyStats.totalBorrowed = records.length
  historyStats.totalReturned = records.filter(r => r.status === 'RETURNED').length
  historyStats.totalFinesPaid = records.reduce((sum, r) => sum + r.fineAmount, 0)

  if (records.length > 0) {
    const totalDays = records.reduce((sum, r) => {
      return sum + calculateDuration(r.borrowDate, r.returnDate)
    }, 0)
    historyStats.avgDaysBorrowed = totalDays / records.length
  }
}

// Lifecycle
onMounted(() => {
  fetchHistory()
})
</script>

<style scoped>
.borrowing-history {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.header-section {
  margin-bottom: 24px;
  text-align: center;
}

.header-section h2 {
  color: #1890ff;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.header-section p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.summary-section {
  margin-bottom: 32px;
}

.stat-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.history-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.loading-container,
.empty-state {
  text-align: center;
  padding: 60px 24px;
}

.book-info {
  line-height: 1.4;
}

.book-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.book-author {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.book-isbn {
  color: #999;
  font-size: 12px;
}

.dates-info {
  line-height: 1.4;
  font-size: 12px;
}

.dates-info > div {
  margin-bottom: 2px;
}

.renewal-info {
  margin-top: 4px;
}

.fine-amount {
  color: #ff4d4f;
  font-weight: 600;
}

.no-fine {
  color: #52c41a;
}

.record-details {
  margin-top: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
  .borrowing-history {
    padding: 16px;
  }

  .summary-section .ant-col {
    margin-bottom: 16px;
  }

  .filter-section {
    padding: 16px;
  }

  .filter-section .ant-row {
    flex-direction: column;
  }

  .filter-section .ant-col {
    width: 100% !important;
    margin-bottom: 8px;
  }

  .history-section {
    padding: 16px;
    overflow-x: auto;
  }
}
</style>

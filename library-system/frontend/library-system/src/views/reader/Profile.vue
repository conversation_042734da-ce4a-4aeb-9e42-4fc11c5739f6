<template>
  <div class="reader-profile">
    <!-- 头部区域 -->
    <div class="header-section">
      <h2>我的个人资料</h2>
      <p>管理您的账户信息和偏好设置</p>
    </div>

    <!-- 资料内容 -->
    <div class="profile-content">
      <a-row :gutter="24">
        <!-- 个人信息 -->
        <a-col :span="16">
          <a-card title="个人信息" class="info-card">
            <a-form
              :model="profileForm"
              :rules="profileRules"
              layout="vertical"
              ref="profileFormRef"
              @finish="updateProfile"
            >
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="用户名" name="username">
                    <a-input
                      v-model:value="profileForm.username"
                      disabled
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="姓名" name="name">
                    <a-input
                      v-model:value="profileForm.name"
                      :placeholder="profileForm.name ? '请输入您的姓名' : `使用用户名：${profileForm.username}`"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="邮箱" name="email">
                    <a-input
                      v-model:value="profileForm.email"
                      placeholder="请输入您的邮箱"
                      type="email"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="电话" name="phone">
                    <a-input
                      v-model:value="profileForm.phone"
                      placeholder="请输入您的电话号码"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-form-item>
                <a-button
                  type="primary"
                  html-type="submit"
                  :loading="updating"
                  size="large"
                >
                  <SaveOutlined />
                  更新资料
                </a-button>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 修改密码 -->
          <a-card title="修改密码" class="password-card">
            <a-form
              :model="passwordForm"
              :rules="passwordRules"
              layout="vertical"
              ref="passwordFormRef"
              @finish="changePassword"
            >
              <a-form-item label="当前密码" name="currentPassword">
                <a-input-password
                  v-model:value="passwordForm.currentPassword"
                  placeholder="请输入当前密码"
                />
              </a-form-item>

              <a-form-item label="新密码" name="newPassword">
                <a-input-password
                  v-model:value="passwordForm.newPassword"
                  placeholder="请输入新密码"
                />
              </a-form-item>

              <a-form-item label="确认新密码" name="confirmPassword">
                <a-input-password
                  v-model:value="passwordForm.confirmPassword"
                  placeholder="请确认新密码"
                />
              </a-form-item>

              <a-form-item>
                <a-button
                  type="primary"
                  html-type="submit"
                  :loading="changingPassword"
                  size="large"
                >
                  <KeyOutlined />
                  修改密码
                </a-button>
              </a-form-item>
            </a-form>
          </a-card>
        </a-col>

        <!-- 账户摘要 -->
        <a-col :span="8">
          <a-card title="账户摘要" class="summary-card">
            <div class="account-info">
              <div class="info-item">
                <span class="label">账户状态：</span>
                <a-tag :color="getStatusColor(accountInfo.status)">
                  {{ formatStatusText(accountInfo.status) }}
                </a-tag>
              </div>

              <div class="info-item">
                <span class="label">注册时间：</span>
                <span class="value">{{ formatDate(accountInfo.createdAt) }}</span>
              </div>

              <div class="info-item">
                <span class="label">累计借阅：</span>
                <span class="value">{{ accountInfo.totalBorrowed }}</span>
              </div>

              <div class="info-item">
                <span class="label">当前借阅：</span>
                <span class="value">{{ accountInfo.currentBorrows }}</span>
              </div>

              <div class="info-item">
                <span class="label">未缴罚款：</span>
                <span class="value" :style="{ color: accountInfo.outstandingFines > 0 ? '#ff4d4f' : '#52c41a' }">
                  ¥{{ accountInfo.outstandingFines.toFixed(2) }}
                </span>
              </div>
            </div>
          </a-card>

          <!-- 快捷操作 -->
          <a-card title="快捷操作" class="actions-card">
            <div class="quick-actions">
              <a-button
                type="primary"
                block
                @click="navigateToSearch"
                class="action-btn"
              >
                <SearchOutlined />
                搜索图书
              </a-button>

              <a-button
                block
                @click="navigateToMyBorrows"
                class="action-btn"
              >
                <BookOutlined />
                我的借阅
              </a-button>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  SaveOutlined,
  KeyOutlined,
  SearchOutlined,
  BookOutlined
} from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import {
  getUserByIdUsingGet,
  updateUserUsingPut,
  changePasswordUsingPost1
} from '@/api/userController'
import type { FormInstance } from 'ant-design-vue'
import dayjs from 'dayjs'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const updating = ref(false)
const changingPassword = ref(false)
const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

const profileForm = reactive({
  username: '',
  name: '',
  email: '',
  phone: ''
})

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const accountInfo = reactive({
  status: 'ACTIVE',
  createdAt: '2024-01-15T10:00:00',
  totalBorrowed: 25,
  currentBorrows: 3,
  outstandingFines: 0
})

// 验证规则
const profileRules = {
  name: [
    { required: true, message: '请输入您的姓名' }
  ],
  email: [
    { type: 'email', message: '请输入有效的邮箱地址' }
  ]
}

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码' },
    { min: 6, message: '密码至少6个字符' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码' },
    {
      validator: (rule: any, value: string) => {
        if (value && value !== passwordForm.newPassword) {
          return Promise.reject('两次输入的密码不一致')
        }
        return Promise.resolve()
      }
    }
  ]
}

// 方法
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY年MM月DD日')
}

// 格式化状态文本（将英文状态转为中文）
const formatStatusText = (status: string) => {
  switch (status) {
    case 'ACTIVE': return '正常'
    case 'FROZEN': return '冻结'
    case 'DEACTIVATED': return '已注销'
    default: return status
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'ACTIVE': return 'green'
    case 'FROZEN': return 'orange'
    case 'DEACTIVATED': return 'red'
    default: return 'default'
  }
}

const updateProfile = async () => {
  if (!authStore.user?.userId) {
    message.error('请先登录')
    return
  }

  try {
    updating.value = true

    const response = await updateUserUsingPut(
      { id: authStore.user.userId },
      {
        username: profileForm.username,
        // Note: API may not support all fields, using available ones
        role: authStore.user.role,
        status: authStore.user.status
      } as any
    )

    if (response.success) {
      message.success('资料更新成功')
    } else {
      message.error(response.message || '资料更新失败')
    }
  } catch (error: any) {
    console.error('Update profile error:', error)
    message.error(error.message || '资料更新失败')
  } finally {
    updating.value = false
  }
}

const changePassword = async () => {
  if (!authStore.user?.userId) {
    message.error('请先登录')
    return
  }

  try {
    changingPassword.value = true

    const response = await changePasswordUsingPost1(
      { id: authStore.user.userId },
      {
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword
      }
    )

    if (response.success) {
      message.success('密码修改成功')
      passwordFormRef.value?.resetFields()
      // 清空表单
      passwordForm.currentPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
    } else {
      message.error(response.message || '密码修改失败')
    }
  } catch (error: any) {
    console.error('Change password error:', error)
    message.error(error.message || '密码修改失败')
  } finally {
    changingPassword.value = false
  }
}

const navigateToSearch = () => {
  router.push('/reader/search')
}

const navigateToMyBorrows = () => {
  router.push('/reader/my-borrows')
}

// 从用户存储初始化资料数据
const initializeProfile = async () => {
  if (!authStore.user?.userId) {
    return
  }

  try {
    // 获取用户详细信息
    const response = await getUserByIdUsingGet({ id: authStore.user.userId })
    if (response.success && response.data) {
      const user = response.data
      profileForm.username = user.username || ''
      profileForm.name = user.name || user.username || ''
      profileForm.email = user.email || ''
      profileForm.phone = user.phone || ''

      // 设置账户信息
      accountInfo.status = user.status || 'ACTIVE'
      accountInfo.createdAt = user.createdAt || '2024-01-15T10:00:00'
    } else {
      // 如果API失败，使用基本信息
      profileForm.username = authStore.user.username || ''
      profileForm.name = authStore.user.username || ''
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    // 使用基本信息作为后备
    profileForm.username = authStore.user.username || ''
    profileForm.name = authStore.user.username || ''
  }
}

// 生命周期
onMounted(() => {
  initializeProfile()
})
</script>

<style scoped>
.reader-profile {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.header-section {
  margin-bottom: 24px;
  text-align: center;
}

.header-section h2 {
  color: #1890ff;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.header-section p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.profile-content {
  margin-top: 24px;
}

.info-card,
.password-card,
.summary-card,
.actions-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.password-card {
  margin-top: 24px;
}

.account-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  color: #666;
  font-weight: 500;
}

.value {
  color: #333;
  font-weight: 600;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reader-profile {
    padding: 16px;
  }

  .profile-content .ant-col {
    margin-bottom: 24px;
  }
}
</style>
<template>
  <div class="book-search">
    <!-- Header Section -->
    <div class="header-section">
      <h2>图书搜索</h2>
      <p>发现您喜欢的书籍</p>
    </div>

    <!-- Search Section -->
    <div class="search-section">
      <a-row :gutter="16">
        <a-col :span="18">
          <a-input-search
            v-model:value="searchForm.keyword"
            placeholder="搜索书名、作者、出版社、ISBN..."
            size="large"
            @search="handleSearch"
            @change="handleSearchChange"
            enter-button
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input-search>
        </a-col>
        <a-col :span="6">
          <a-button
            size="large"
            @click="showAdvancedSearch = !showAdvancedSearch"
            block
          >
            <FilterOutlined />
            {{ showAdvancedSearch ? '隐藏' : '高级' }}筛选
          </a-button>
        </a-col>
      </a-row>

      <!-- Advanced Search Filters -->
      <div v-if="showAdvancedSearch" class="advanced-filters">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="分类">
              <a-select
                v-model:value="searchForm.categoryId"
                placeholder="所有分类"
                @change="handleSearch"
                allow-clear
              >
                <a-select-option
                  v-for="category in categories"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="可借状态">
              <a-select
                v-model:value="searchForm.availability"
                placeholder="所有图书"
                @change="handleSearch"
                allow-clear
              >
                <a-select-option value="available">仅显示可借图书</a-select-option>
                <a-select-option value="unavailable">显示不可借图书</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="排序方式">
              <a-select
                v-model:value="searchForm.sortBy"
                placeholder="相关性"
                @change="handleSearch"
              >
                <a-select-option value="relevance">相关性</a-select-option>
                <a-select-option value="title">书名 A-Z</a-select-option>
                <a-select-option value="author">作者 A-Z</a-select-option>
                <a-select-option value="newest">最新</a-select-option>
                <a-select-option value="popular">最热门</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="操作">
              <a-button @click="resetFilters" block>
                <ReloadOutlined />
                重置筛选
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- Search Results -->
    <div class="results-section">
      <!-- Results Header -->
      <div class="results-header">
        <div class="results-info">
          <span v-if="!loading">
            找到 {{ pagination.total }} 本书
            <span v-if="searchForm.keyword">关于 "{{ searchForm.keyword }}"</span>
          </span>
        </div>
        <div class="view-toggle">
          <a-radio-group v-model:value="viewMode" button-style="solid">
            <a-radio-button value="grid">
              <AppstoreOutlined />
              网格
            </a-radio-button>
            <a-radio-button value="list">
              <UnorderedListOutlined />
              列表
            </a-radio-button>
          </a-radio-group>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="loading-container">
        <a-spin size="large" />
      </div>

      <!-- Empty State -->
      <div v-else-if="books.length === 0" class="empty-state">
        <a-empty :description="getEmptyStateMessage()">
          <a-button type="primary" @click="resetFilters">
            清除筛选
          </a-button>
        </a-empty>
      </div>

      <!-- Grid View -->
      <div v-else-if="viewMode === 'grid'" class="grid-view">
        <a-row :gutter="[16, 16]">
          <a-col
            v-for="book in books"
            :key="book.id"
            :xs="24"
            :sm="12"
            :md="8"
            :lg="6"
          >
            <a-card class="book-card" hoverable>
              <div class="book-cover">
                <BookOutlined class="cover-icon" />
              </div>
              <div class="book-info">
                <h4 class="book-title">{{ book.title }}</h4>
                <p class="book-author">{{ book.author }}</p>
                <p class="book-category">{{ book.categoryName }}</p>
                <div class="book-details">
                  <p class="book-isbn">ISBN: {{ book.isbn }}</p>
                  <p class="book-publisher">{{ book.publisher }}</p>
                </div>
                <div class="availability-info">
                  <a-tag
                    :color="book.availableCount > 0 ? 'green' : 'red'"
                    class="availability-tag"
                  >
                    {{ book.availableCount > 0 ? '可借' : '不可借' }}
                  </a-tag>
                  <span class="stock-info">
                    {{ book.availableCount }} / {{ book.totalCount }} 可借
                  </span>
                </div>
                <div class="book-actions">
                  <a-button
                    type="primary"
                    block
                    :disabled="book.availableCount === 0"
                    @click="borrowBook(book.id)"
                    :loading="borrowingBooks.includes(book.id)"
                  >
                    {{ book.availableCount > 0 ? '借阅' : '不可借' }}
                  </a-button>
                  <a-button
                    type="link"
                    block
                    @click="viewBookDetails(book)"
                  >
                    <EyeOutlined />
                    查看详情
                  </a-button>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- Pagination -->
      <div class="pagination-section">
        <a-pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="(total, range) => `${range[0]}-${range[1]} 共 ${total} 本书`"
          @change="handlePaginationChange"
          @show-size-change="handlePaginationChange"
        />
      </div>
    </div>

    <!-- Book Details Modal -->
    <a-modal
      v-model:open="detailsModalVisible"
      title="图书详情"
      :footer="null"
      width="600px"
    >
      <div v-if="selectedBook" class="book-details-modal">
        <div class="modal-book-cover">
          <BookOutlined class="modal-cover-icon" />
        </div>
        <div class="modal-book-info">
          <h3>{{ selectedBook.title }}</h3>
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="作者">
              {{ selectedBook.author }}
            </a-descriptions-item>
            <a-descriptions-item label="ISBN">
              {{ selectedBook.isbn }}
            </a-descriptions-item>
            <a-descriptions-item label="出版社">
              {{ selectedBook.publisher }}
            </a-descriptions-item>
            <a-descriptions-item label="分类">
              {{ selectedBook.categoryName }}
            </a-descriptions-item>
            <a-descriptions-item label="状态">
              <a-tag :color="selectedBook.status === 'AVAILABLE' ? 'green' : 'red'">
                {{ selectedBook.status === 'AVAILABLE' ? '可借' : '不可借' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="可借数量">
              {{ selectedBook.availableCount }} / {{ selectedBook.totalCount }} 本可借
            </a-descriptions-item>
          </a-descriptions>
          <div class="modal-actions">
            <a-button
              type="primary"
              size="large"
              :disabled="selectedBook.availableCount === 0"
              @click="borrowBook(selectedBook.id)"
              :loading="borrowingBooks.includes(selectedBook.id)"
              block
            >
              {{ selectedBook.availableCount > 0 ? '借阅' : '目前不可借' }}
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  BookOutlined,
  EyeOutlined
} from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import {
  searchBooksUsingGet,
  getBooksUsingGet
} from '@/api/bookController'
import {
  borrowBookUsingPost,
  canReaderBorrowMoreUsingGet
} from '@/api/borrowController'

const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const borrowingBooks = ref<number[]>([])
const showAdvancedSearch = ref(false)
const viewMode = ref<'grid' | 'list'>('grid')
const detailsModalVisible = ref(false)
const selectedBook = ref<any>(null)

const books = ref<any[]>([])
const categories = ref<any[]>([
  { id: 1, name: '文学', description: '文学类图书' },
  { id: 2, name: '科学', description: '科学类图书' },
  { id: 3, name: '历史', description: '历史类图书' },
  { id: 4, name: '艺术', description: '艺术类图书' },
  { id: 5, name: '技术', description: '技术类图书' }
])

// Search form
const searchForm = reactive({
  keyword: '',
  categoryId: undefined,
  availability: undefined,
  sortBy: 'relevance'
})

// Pagination
const pagination = reactive({
  current: 1,
  pageSize: 12,
  total: 0
})

// Methods
const handleSearchChange = () => {
  // Debounce search
  setTimeout(() => {
    handleSearch()
  }, 300)
}

const getEmptyStateMessage = () => {
  if (searchForm.keyword.trim()) {
    return `没有找到关于 "${searchForm.keyword}" 的图书`
  }
  if (searchForm.categoryId || searchForm.availability) {
    return '没有符合筛选条件的图书'
  }
  return '没有找到图书'
}

const handleSearch = async () => {
  try {
    loading.value = true
    pagination.current = 1

    // 构建搜索参数
    const params: any = {
      current: pagination.current,
      size: pagination.pageSize,
      categoryId: searchForm.categoryId || undefined
    }

    // 如果有关键词，先尝试按书名搜索
    if (searchForm.keyword) {
      params.title = searchForm.keyword
    }

    console.log('Search params:', params)

    // Use real API
    const response = await searchBooksUsingGet(params)
    console.log('Search response:', response)

    if (response.success && response.data) {
      let searchResults = response.data.records || []

      // 如果按书名搜索没有结果，再尝试按作者搜索
      if (searchResults.length === 0 && searchForm.keyword) {
        const authorParams = {
          ...params,
          title: undefined,
          author: searchForm.keyword
        }
        const authorResponse = await searchBooksUsingGet(authorParams)
        if (authorResponse.success && authorResponse.data) {
          searchResults = authorResponse.data.records || []
        }
      }

      books.value = searchResults
      pagination.current = response.data.current || 1
      pagination.total = response.data.total || 0

      // Apply client-side availability filter if needed
      if (searchForm.availability) {
        if (searchForm.availability === 'available') {
          books.value = books.value.filter(book => book.availableCount > 0)
        } else if (searchForm.availability === 'unavailable') {
          books.value = books.value.filter(book => book.availableCount === 0)
        }
        pagination.total = books.value.length
      }
    }
  } catch (error) {
    console.error('Search error:', error)
    message.error('搜索图书失败')
  } finally {
    loading.value = false
  }
}

const resetFilters = () => {
  searchForm.keyword = ''
  searchForm.categoryId = undefined
  searchForm.availability = undefined
  searchForm.sortBy = 'relevance'
  pagination.current = 1
  handleSearch()
}

const handlePaginationChange = (page: number, pageSize: number) => {
  pagination.current = page
  pagination.pageSize = pageSize
  handleSearch()
}

const borrowBook = async (bookId: number) => {
  if (!authStore.user?.userId) {
    message.error('请先登录')
    return
  }

  try {
    borrowingBooks.value.push(bookId)

    // Check if reader can borrow more books
    const canBorrowResponse = await canReaderBorrowMoreUsingGet({
      readerId: authStore.user.userId
    })

    if (!canBorrowResponse.success || !canBorrowResponse.data) {
      message.error('您已达到借阅上限')
      return
    }

    // Borrow the book
    const borrowResponse = await borrowBookUsingPost({
      readerId: authStore.user.userId,
      bookId: bookId
    })

    if (borrowResponse.success) {
      message.success('借阅成功！')
      handleSearch() // Refresh the list to show updated availability
      detailsModalVisible.value = false
    } else {
      message.error(borrowResponse.message || '借阅失败')
    }
  } catch (error: any) {
    console.error('Borrow error:', error)
    message.error(error.message || '借阅失败')
  } finally {
    borrowingBooks.value = borrowingBooks.value.filter(id => id !== bookId)
  }
}

const viewBookDetails = (book: any) => {
  selectedBook.value = book
  detailsModalVisible.value = true
}

// Lifecycle
onMounted(() => {
  handleSearch() // Initial search to load all books
})
</script>

<style scoped>
.book-search {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.header-section {
  margin-bottom: 24px;
  text-align: center;
}

.header-section h2 {
  color: #1890ff;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.header-section p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.search-section {
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.advanced-filters {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.results-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.results-info {
  font-size: 14px;
  color: #666;
}

.loading-container,
.empty-state {
  text-align: center;
  padding: 60px 24px;
}

.grid-view {
  padding: 24px;
}

.book-card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
}

.book-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.book-cover {
  text-align: center;
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-bottom: 1px solid #f0f0f0;
}

.cover-icon {
  font-size: 48px;
  color: #1890ff;
}

.book-info {
  padding: 16px;
}

.book-title {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.book-author {
  margin: 0 0 4px 0;
  color: #666;
  font-size: 14px;
}

.book-category {
  margin: 0 0 8px 0;
  color: #999;
  font-size: 12px;
}

.book-details {
  margin-bottom: 12px;
}

.book-details p {
  margin: 0 0 4px 0;
  color: #666;
  font-size: 12px;
}

.availability-info {
  margin-bottom: 16px;
}

.availability-tag {
  margin-bottom: 4px;
}

.stock-info {
  display: block;
  color: #999;
  font-size: 12px;
}

.book-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.pagination-section {
  padding: 24px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
}

.book-details-modal {
  display: flex;
  gap: 24px;
}

.modal-book-cover {
  flex-shrink: 0;
  width: 120px;
  height: 160px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-cover-icon {
  font-size: 48px;
  color: #1890ff;
}

.modal-book-info {
  flex: 1;
}

.modal-book-info h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.modal-actions {
  margin-top: 24px;
}

/* Responsive design */
@media (max-width: 768px) {
  .book-search {
    padding: 16px;
  }

  .search-section {
    padding: 16px;
  }

  .results-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .grid-view {
    padding: 16px;
  }

  .book-details-modal {
    flex-direction: column;
  }

  .modal-book-cover {
    width: 100%;
    height: 120px;
  }
}
</style>

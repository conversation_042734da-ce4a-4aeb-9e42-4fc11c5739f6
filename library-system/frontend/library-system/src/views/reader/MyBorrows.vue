<template>
  <div class="my-borrows">
    <!-- Header Section -->
    <div class="header-section">
      <h2>我的借阅</h2>
      <p>管理您当前借阅的图书</p>
    </div>

    <!-- Summary Cards -->
    <div class="summary-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="summary-card">
            <a-statistic
              title="当前借阅"
              :value="summary.currentBorrows"
              :value-style="{ color: '#1890ff' }"
            >
              <template #suffix>
                <BookOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="summary-card">
            <a-statistic
              title="即将到期"
              :value="summary.dueSoon"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #suffix>
                <ClockCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="summary-card">
            <a-statistic
              title="已逾期"
              :value="summary.overdue"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #suffix>
                <ExclamationCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="summary-card">
            <a-statistic
              title="罚金总额"
              :value="summary.totalFines"
              :precision="2"
              prefix="¥"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #suffix>
                <DollarOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="搜索书名或作者"
            @change="handleSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="8">
          <a-select
            v-model:value="searchForm.status"
            placeholder="所有状态"
            @change="handleSearch"
            allow-clear
            style="width: 100%"
          >
            <a-select-option value="BORROWED">借阅中</a-select-option>
            <a-select-option value="OVERDUE">已逾期</a-select-option>
            <a-select-option value="RENEWED">已续借</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-button @click="resetFilters">
            <ReloadOutlined />
            重置
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- Borrows Table -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :data-source="borrowRecords"
        :loading="loading"
        :pagination="paginationConfig"
        @change="handleTableChange"
        row-key="id"
      >
        <!-- Book Column -->
        <template #book="{ record }">
          <div class="book-info">
            <div class="book-title">{{ record.bookTitle }}</div>
            <div class="book-author">{{ record.bookAuthor }}</div>
            <div class="book-isbn">ISBN: {{ record.isbn }}</div>
          </div>
        </template>

        <!-- Dates Column -->
        <template #dates="{ record }">
          <div class="dates-info">
            <div><strong>借阅日期:</strong> {{ formatDate(record.borrowDate) }}</div>
            <div><strong>到期日期:</strong> {{ formatDate(record.dueDate) }}</div>
            <div v-if="record.renewalCount > 0" class="renewal-info">
              <a-tag color="orange" size="small">
                已续借 {{ record.renewalCount }} 次
              </a-tag>
            </div>
          </div>
        </template>

        <!-- Status Column -->
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
          <div v-if="record.fineAmount > 0" class="fine-amount">
            罚金: ¥{{ record.fineAmount.toFixed(2) }}
          </div>
        </template>

        <!-- Days Left Column -->
        <template #daysLeft="{ record }">
          <span :class="getDaysLeftClass(record)">
            {{ getDaysLeft(record.dueDate) }}
          </span>
        </template>

        <!-- Actions Column -->
        <template #actions="{ record }">
          <a-space>
            <a-button
              type="primary"
              size="small"
              @click="renewBook(record.id)"
              :loading="renewingBooks.includes(record.id)"
              :disabled="!canRenew(record)"
            >
              续借
            </a-button>
            <a-button
              size="small"
              @click="viewDetails(record)"
            >
              <EyeOutlined />
              详情
            </a-button>
          </a-space>
        </template>
      </a-table>
    </div>

    <!-- Details Modal -->
    <a-modal
      v-model:open="detailsModalVisible"
      title="借阅详情"
      :footer="null"
      width="600px"
    >
      <div v-if="selectedRecord" class="record-details">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="借阅记录ID">
            {{ selectedRecord.id }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="图书标题" :span="2">
            {{ selectedRecord.bookTitle }}
          </a-descriptions-item>
          <a-descriptions-item label="作者">
            {{ selectedRecord.bookAuthor }}
          </a-descriptions-item>
          <a-descriptions-item label="ISBN">
            {{ selectedRecord.isbn }}
          </a-descriptions-item>
          <a-descriptions-item label="借阅日期">
            {{ formatDate(selectedRecord.borrowDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="到期日期">
            {{ formatDate(selectedRecord.dueDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="续借次数">
            {{ selectedRecord.renewalCount }}
          </a-descriptions-item>
          <a-descriptions-item label="罚金金额">
            <span :style="{ color: selectedRecord.fineAmount > 0 ? '#ff4d4f' : '#52c41a' }">
              ¥{{ selectedRecord.fineAmount.toFixed(2) }}
            </span>
          </a-descriptions-item>
        </a-descriptions>
        
        <div class="modal-actions">
          <a-space>
            <a-button
              type="primary"
              @click="renewBook(selectedRecord.id)"
              :loading="renewingBooks.includes(selectedRecord.id)"
              :disabled="!canRenew(selectedRecord)"
            >
              续借图书
            </a-button>
            <a-button @click="detailsModalVisible = false">
              关闭
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  BookOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  DollarOutlined,
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined
} from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { 
  getBorrowRecordsUsingGet,
  renewBookUsingPost 
} from '@/api/borrowController'
import dayjs from 'dayjs'

const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const borrowRecords = ref<any[]>([])
const selectedRecord = ref<any>(null)
const detailsModalVisible = ref(false)
const renewingBooks = ref<number[]>([])

// Summary statistics
const summary = reactive({
  currentBorrows: 0,
  dueSoon: 0,
  overdue: 0,
  totalFines: 0
})

// Search and filter
const searchForm = reactive({
  keyword: '',
  status: undefined
})

// Pagination
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// Table columns
const columns = [
  {
    title: '图书信息',
    key: 'book',
    slots: { customRender: 'book' },
    width: 250
  },
  {
    title: '借阅信息',
    key: 'dates',
    slots: { customRender: 'dates' },
    width: 200
  },
  {
    title: '状态',
    key: 'status',
    slots: { customRender: 'status' },
    width: 120
  },
  {
    title: '剩余天数',
    key: 'daysLeft',
    slots: { customRender: 'daysLeft' },
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    slots: { customRender: 'actions' },
    width: 150
  }
]

// Computed
const paginationConfig = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: number[]) => 
    `${range[0]}-${range[1]} 共 ${total} 条记录`
}))

// Methods
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY年MM月DD日')
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'BORROWED': return 'blue'
    case 'OVERDUE': return 'red'
    case 'RENEWED': return 'orange'
    default: return 'default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'BORROWED': return '借阅中'
    case 'OVERDUE': return '已逾期'
    case 'RENEWED': return '已续借'
    default: return status
  }
}

const getDaysLeft = (dueDate: string) => {
  const due = dayjs(dueDate)
  const now = dayjs()
  const diff = due.diff(now, 'day')

  if (diff < 0) {
    return `逾期 ${Math.abs(diff)} 天`
  } else if (diff === 0) {
    return '今天到期'
  } else if (diff <= 3) {
    return `${diff} 天后到期`
  } else {
    return `${diff} 天后到期`
  }
}

const getDaysLeftClass = (record: any) => {
  const due = dayjs(record.dueDate)
  const now = dayjs()
  const diff = due.diff(now, 'day')

  if (diff < 0) {
    return 'overdue'
  } else if (diff <= 3) {
    return 'due-soon'
  } else {
    return 'normal'
  }
}

const canRenew = (record: any) => {
  return record.status === 'BORROWED' && record.renewalCount < 2
}

const handleSearch = () => {
  pagination.current = 1
  fetchBorrowRecords()
}

const resetFilters = () => {
  searchForm.keyword = ''
  searchForm.status = undefined
  pagination.current = 1
  fetchBorrowRecords()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchBorrowRecords()
}

const fetchBorrowRecords = async () => {
  if (!authStore.user?.id) {
    message.error('请先登录')
    return
  }

  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      readerId: authStore.user.id,
      status: searchForm.status || 'BORROWED', // Only get current borrows
      keyword: searchForm.keyword || undefined
    }

    const response = await getBorrowRecordsUsingGet(params)
    if (response.success && response.data) {
      borrowRecords.value = response.data.records || []
      pagination.current = response.data.current || 1
      pagination.total = response.data.total || 0

      // Calculate summary statistics
      calculateSummary()
    }
  } catch (error) {
    console.error('Fetch borrows error:', error)
    message.error('获取借阅记录失败')
  } finally {
    loading.value = false
  }
}

const calculateSummary = () => {
  const records = borrowRecords.value
  summary.currentBorrows = records.length
  summary.overdue = records.filter(r => r.status === 'OVERDUE').length
  summary.totalFines = records.reduce((sum, r) => sum + (r.fineAmount || 0), 0)

  // Calculate due soon (within 3 days)
  summary.dueSoon = records.filter(r => {
    const due = dayjs(r.dueDate)
    const now = dayjs()
    const diff = due.diff(now, 'day')
    return diff >= 0 && diff <= 3
  }).length
}

const renewBook = async (recordId: number) => {
  try {
    renewingBooks.value.push(recordId)

    const response = await renewBookUsingPost({ recordId })
    if (response.success) {
      message.success('续借成功！')
      fetchBorrowRecords() // Refresh the list
      detailsModalVisible.value = false
    } else {
      message.error(response.message || '续借失败')
    }
  } catch (error: any) {
    console.error('Renew error:', error)
    message.error(error.message || '续借失败')
  } finally {
    renewingBooks.value = renewingBooks.value.filter(id => id !== recordId)
  }
}

const viewDetails = (record: any) => {
  selectedRecord.value = record
  detailsModalVisible.value = true
}

// Lifecycle
onMounted(() => {
  fetchBorrowRecords()
})
</script>

<style scoped>
.my-borrows {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.header-section {
  margin-bottom: 24px;
  text-align: center;
}

.header-section h2 {
  color: #1890ff;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.header-section p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.summary-section {
  margin-bottom: 32px;
}

.summary-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.book-info {
  line-height: 1.4;
}

.book-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.book-author {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.book-isbn {
  color: #999;
  font-size: 12px;
}

.dates-info {
  line-height: 1.4;
  font-size: 12px;
}

.dates-info > div {
  margin-bottom: 2px;
}

.renewal-info {
  margin-top: 4px;
}

.fine-amount {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.overdue {
  color: #ff4d4f;
  font-weight: 600;
}

.due-soon {
  color: #fa8c16;
  font-weight: 600;
}

.normal {
  color: #52c41a;
}

.record-details {
  margin-top: 16px;
}

.modal-actions {
  margin-top: 24px;
  text-align: right;
}

/* Responsive design */
@media (max-width: 768px) {
  .my-borrows {
    padding: 16px;
  }

  .summary-section .ant-col {
    margin-bottom: 16px;
  }

  .filter-section {
    padding: 16px;
  }

  .filter-section .ant-row {
    flex-direction: column;
  }

  .filter-section .ant-col {
    width: 100% !important;
    margin-bottom: 8px;
  }

  .table-section {
    padding: 16px;
    overflow-x: auto;
  }
}
</style>

<template>
  <div class="reader-dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1>欢迎回来，{{ authStore.user?.username }}！</h1>
        <p>管理您的图书馆账户并发现新书籍</p>
      </div>
      <div class="quick-stats">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-card class="stat-card">
              <a-statistic
                title="当前借阅"
                :value="dashboardStats.currentBorrows"
                :value-style="{ color: '#1890ff' }"
              >
                <template #suffix>
                  <BookOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card class="stat-card">
              <a-statistic
                title="即将到期"
                :value="dashboardStats.dueSoon"
                :value-style="{ color: '#fa8c16' }"
              >
                <template #suffix>
                  <ClockCircleOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card class="stat-card">
              <a-statistic
                title="可借数量"
                :value="dashboardStats.availableSlots"
                :value-style="{ color: '#52c41a' }"
              >
                <template #suffix>
                  <CheckCircleOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions">
      <h3>快捷操作</h3>
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="action-card" @click="navigateToSearch">
            <div class="action-content">
              <SearchOutlined class="action-icon" />
              <h4>搜索图书</h4>
              <p>寻找您的下一本好书</p>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="action-card" @click="navigateToMyBorrows">
            <div class="action-content">
              <BookOutlined class="action-icon" />
              <h4>我的借阅</h4>
              <p>查看当前借阅的图书</p>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="action-card" @click="navigateToHistory">
            <div class="action-content">
              <HistoryOutlined class="action-icon" />
              <h4>借阅历史</h4>
              <p>查看过往借阅记录</p>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="action-card" @click="navigateToProfile">
            <div class="action-content">
              <UserOutlined class="action-icon" />
              <h4>个人资料</h4>
              <p>管理您的账户信息</p>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 当前借阅 -->
    <div class="current-borrowings">
      <div class="section-header">
        <h3>当前借阅</h3>
        <a-button type="link" @click="navigateToMyBorrows">
          查看全部
          <RightOutlined />
        </a-button>
      </div>

      <div v-if="loading" class="loading-container">
        <a-spin size="large" />
      </div>

      <div v-else-if="currentBorrows.length === 0" class="empty-state">
        <a-empty description="当前没有借阅书籍">
          <a-button type="primary" @click="navigateToSearch">
            浏览图书
          </a-button>
        </a-empty>
      </div>

      <a-row v-else :gutter="16">
        <a-col
          v-for="borrow in currentBorrows.slice(0, 4)"
          :key="borrow.id"
          :span="6"
        >
          <a-card class="borrow-card">
            <div class="book-info">
              <h4>{{ borrow.bookTitle }}</h4>
              <p class="author">{{ borrow.bookAuthor }}</p>
              <div class="due-info">
                <a-tag
                  :color="getDueDateColor(borrow.dueDate)"
                  class="due-tag"
                >
                  到期日: {{ formatDate(borrow.dueDate) }}
                </a-tag>
              </div>
              <div class="book-actions">
                <a-button
                  v-if="canRenew(borrow)"
                  type="link"
                  size="small"
                  @click="renewBook(borrow.id)"
                  :loading="renewingBooks.includes(borrow.id)"
                >
                  <SyncOutlined />
                  续借
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  @click="viewBookDetails(borrow)"
                >
                  <EyeOutlined />
                  详情
                </a-button>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity">
      <h3>最近活动</h3>
      <a-timeline>
        <a-timeline-item
          v-for="activity in recentActivity"
          :key="activity.id"
          :color="getActivityColor(activity.type)"
        >
          <template #dot>
            <component :is="getActivityIcon(activity.type)" />
          </template>
          <div class="activity-content">
            <p><strong>{{ activity.title }}</strong></p>
            <p class="activity-description">{{ activity.description }}</p>
            <span class="activity-time">{{ formatDateTime(activity.timestamp) }}</span>
          </div>
        </a-timeline-item>
      </a-timeline>
    </div>

    <!-- 推荐书籍 -->
    <div class="recommendations">
      <h3>为您推荐</h3>
      <a-row :gutter="16">
        <a-col
          v-for="book in recommendedBooks"
          :key="book.id"
          :span="6"
        >
          <a-card class="book-card" hoverable>
            <div class="book-cover">
              <BookOutlined class="cover-icon" />
            </div>
            <div class="book-details">
              <h4>{{ book.title }}</h4>
              <p class="author">{{ book.author }}</p>
              <p class="category">{{ book.categoryName }}</p>
              <div class="book-status">
                <a-tag
                  :color="book.availableCount > 0 ? 'green' : 'red'"
                  size="small"
                >
                  {{ book.availableCount > 0 ? '可借阅' : '不可借阅' }}
                </a-tag>
              </div>
              <a-button
                type="primary"
                size="small"
                block
                :disabled="book.availableCount === 0"
                @click="borrowBook(book.id)"
                :loading="borrowingBooks.includes(book.id)"
              >
                {{ book.availableCount > 0 ? '借阅' : '不可借' }}
              </a-button>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  BookOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  SearchOutlined,
  HistoryOutlined,
  UserOutlined,
  RightOutlined,
  SyncOutlined,
  EyeOutlined
} from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import {
  getCurrentBorrowsByReaderUsingGet,
  searchBorrowRecordsUsingGet
} from '@/api/borrowController'
import {
  getBookByIdUsingGet
} from '@/api/bookController'
import dayjs from 'dayjs'

const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const renewingBooks = ref<number[]>([])
const borrowingBooks = ref<number[]>([])
const dashboardStats = reactive({
  currentBorrows: 0,
  dueSoon: 0,
  availableSlots: 5
})

const currentBorrows = ref<any[]>([])
const recentActivity = ref<any[]>([])
const recommendedBooks = ref<any[]>([
  {
    id: 1,
    title: '推荐图书1',
    author: '作者1',
    availableCount: 3,
    totalCount: 5
  },
  {
    id: 2,
    title: '推荐图书2',
    author: '作者2',
    availableCount: 0,
    totalCount: 2
  }
])

// Real API functions
const fetchDashboardData = async () => {
  if (!authStore.user?.userId) {
    return
  }

  try {
    loading.value = true

    // Fetch current borrows using the correct API
    const borrowsResponse = await getCurrentBorrowsByReaderUsingGet({
      readerId: authStore.user.userId
    })

    if (borrowsResponse.success && borrowsResponse.data) {
      const borrowRecords = borrowsResponse.data || []

      // Fetch book details for each borrow record
      const borrowsWithBookInfo = await Promise.all(
        borrowRecords.map(async (record: any) => {
          try {
            if (record.bookId) {
              const bookResponse = await getBookByIdUsingGet({ id: record.bookId })
              if (bookResponse.success && bookResponse.data) {
                return {
                  ...record,
                  bookTitle: bookResponse.data.title || '未知书名',
                  bookAuthor: bookResponse.data.author || '未知作者'
                }
              }
            }
            return {
              ...record,
              bookTitle: '未知书名',
              bookAuthor: '未知作者'
            }
          } catch (error) {
            console.error('获取书籍信息失败:', error)
            return {
              ...record,
              bookTitle: '未知书名',
              bookAuthor: '未知作者'
            }
          }
        })
      )

      currentBorrows.value = borrowsWithBookInfo

      // Calculate statistics
      const records = borrowsWithBookInfo
      dashboardStats.currentBorrows = records.length
      dashboardStats.availableSlots = Math.max(0, 5 - records.length) // Assuming max 5 books

      // Calculate due soon (within 3 days)
      dashboardStats.dueSoon = records.filter(record => {
        const due = dayjs(record.dueDate)
        const now = dayjs()
        const diff = due.diff(now, 'day')
        return diff >= 0 && diff <= 3
      }).length

      // Set recent activity (same as current borrows for now)
      recentActivity.value = records.slice(0, 5).map(record => ({
        id: record.id,
        type: 'BORROW',
        title: `借阅了《${record.bookTitle}》`,
        description: `从图书馆成功借阅`,
        timestamp: record.borrowDate
      }))
    }
  } catch (error) {
    console.error('Fetch dashboard data error:', error)
  } finally {
    loading.value = false
  }
}

// Methods
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('MM月DD日')
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'BORROWED': return 'blue'
    case 'OVERDUE': return 'red'
    case 'RENEWED': return 'orange'
    default: return 'default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'BORROWED': return '借阅中'
    case 'OVERDUE': return '已逾期'
    case 'RENEWED': return '已续借'
    default: return status
  }
}

const getDueDateColor = (dueDate: string) => {
  const due = dayjs(dueDate)
  const now = dayjs()
  const diff = due.diff(now, 'day')

  if (diff < 0) {
    return 'red' // Overdue
  } else if (diff <= 3) {
    return 'orange' // Due soon
  } else {
    return 'green' // Normal
  }
}

const canRenew = (record: any) => {
  return record.status === 'BORROWED' && record.renewalCount < 2
}

const renewBook = async (recordId: number) => {
  // This would call the real API
  console.log('Renewing book:', recordId)
  message.info('续借功能开发中...')
}

const viewBookDetails = (book: any) => {
  console.log('Viewing book details:', book)
  message.info('图书详情功能开发中...')
}

const getActivityColor = (type: string) => {
  switch (type) {
    case 'borrow': return 'blue'
    case 'return': return 'green'
    case 'renew': return 'orange'
    default: return 'gray'
  }
}

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'borrow': return 'BookOutlined'
    case 'return': return 'CheckCircleOutlined'
    case 'renew': return 'SyncOutlined'
    default: return 'InfoCircleOutlined'
  }
}

// Navigation methods
const navigateToSearch = () => {
  router.push('/reader/search')
}

const navigateToMyBorrows = () => {
  router.push('/reader/my-borrows')
}

const navigateToHistory = () => {
  router.push('/reader/history')
}

const navigateToProfile = () => {
  router.push('/reader/profile')
}

const formatDateTime = (dateString: string) => {
  return dayjs(dateString).format('MM月DD日 HH:mm')
}

const borrowBook = async (bookId: number) => {
  console.log('Borrowing book:', bookId)
  message.info('借阅功能开发中...')
}

// Lifecycle
onMounted(() => {
  fetchDashboardData()
})
</script>

<style scoped>
.reader-dashboard {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 32px;
  text-align: center;
}

.welcome-section h1 {
  color: #1890ff;
  margin-bottom: 8px;
  font-size: 32px;
  font-weight: 600;
}

.welcome-section p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.quick-stats {
  margin-bottom: 32px;
}

.stat-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-actions {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.action-card {
  text-align: center;
  padding: 24px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.action-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  transform: translateY(-2px);
}

.action-icon {
  font-size: 32px;
  color: #1890ff;
  margin-bottom: 8px;
}

.action-card h4 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.action-card p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.current-borrowings,
.recent-activity {
  margin-bottom: 32px;
}

.section-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.borrow-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.borrow-item:last-child {
  border-bottom: none;
}

.book-info h4 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.book-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.borrow-meta {
  text-align: right;
  font-size: 12px;
  color: #999;
}

.due-date {
  margin-bottom: 4px;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 14px;
  color: #1890ff;
}

.activity-content {
  flex: 1;
}

.activity-content h5 {
  margin: 0 0 2px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.activity-content p {
  margin: 0;
  color: #666;
  font-size: 12px;
}

.activity-time {
  color: #999;
  font-size: 12px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reader-dashboard {
    padding: 16px;
  }

  .quick-stats .ant-col,
  .quick-actions .ant-col,
  .current-borrowings .ant-col,
  .recommendations .ant-col {
    margin-bottom: 16px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .action-card {
    height: 100px;
  }

  .action-icon {
    font-size: 24px;
  }
}
</style>

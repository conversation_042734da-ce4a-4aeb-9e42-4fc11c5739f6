// @ts-ignore
/* eslint-disable */
import request from '@/request'

/** getBooks GET /api/books */
export async function getBooksUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getBooksUsingGETParams,
  options?: { [key: string]: any }
) {
  return request<API.ApiResponseIPageBook_>('/api/books', {
    method: 'GET',
    params: {
      // current has a default value: 1
      current: '1',
      // size has a default value: 10
      size: '10',
      ...params,
    },
    ...(options || {}),
  })
}

/** createBook POST /api/books */
export async function createBookUsingPost(body: API.Book1, options?: { [key: string]: any }) {
  return request<API.ApiResponseBook_>('/api/books', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** getBookById GET /api/books/${param0} */
export async function getBookByIdUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getBookByIdUsingGETParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params
  return request<API.ApiResponseBook_>(`/api/books/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** updateBook PUT /api/books/${param0} */
export async function updateBookUsingPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateBookUsingPUTParams,
  body: API.Book1,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params
  return request<API.ApiResponseBook_>(`/api/books/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  })
}

/** deleteBook DELETE /api/books/${param0} */
export async function deleteBookUsingDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteBookUsingDELETEParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params
  return request<API.ApiResponseVoid_>(`/api/books/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** isBookAvailableForBorrow GET /api/books/${param0}/available-for-borrow */
export async function isBookAvailableForBorrowUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.isBookAvailableForBorrowUsingGETParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params
  return request<API.ApiResponseBoolean_>(`/api/books/${param0}/available-for-borrow`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** updateBookInventory POST /api/books/${param0}/inventory */
export async function updateBookInventoryUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateBookInventoryUsingPOSTParams,
  body: Record<string, any>,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params
  return request<API.ApiResponseVoid_>(`/api/books/${param0}/inventory`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  })
}

/** updateBookStatus POST /api/books/${param0}/status */
export async function updateBookStatusUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateBookStatusUsingPOSTParams,
  body: Record<string, any>,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params
  return request<API.ApiResponseVoid_>(`/api/books/${param0}/status`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  })
}

/** getAvailableBooks GET /api/books/available */
export async function getAvailableBooksUsingGet(options?: { [key: string]: any }) {
  return request<API.ApiResponseListBook_>('/api/books/available', {
    method: 'GET',
    ...(options || {}),
  })
}

/** getBooksByCategory GET /api/books/category/${param0} */
export async function getBooksByCategoryUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getBooksByCategoryUsingGETParams,
  options?: { [key: string]: any }
) {
  const { categoryId: param0, ...queryParams } = params
  return request<API.ApiResponseListBook_>(`/api/books/category/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** checkIsbn GET /api/books/check-isbn */
export async function checkIsbnUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.checkIsbnUsingGETParams,
  options?: { [key: string]: any }
) {
  return request<API.ApiResponseBoolean_>('/api/books/check-isbn', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  })
}

/** getBookByIsbn GET /api/books/isbn/${param0} */
export async function getBookByIsbnUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getBookByIsbnUsingGETParams,
  options?: { [key: string]: any }
) {
  const { isbn: param0, ...queryParams } = params
  return request<API.ApiResponseBook_>(`/api/books/isbn/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** searchBooks GET /api/books/search */
export async function searchBooksUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.searchBooksUsingGETParams,
  options?: { [key: string]: any }
) {
  return request<API.ApiResponseIPageBook_>('/api/books/search', {
    method: 'GET',
    params: {
      // current has a default value: 1
      current: '1',
      // size has a default value: 10
      size: '10',

      ...params,
    },
    ...(options || {}),
  })
}

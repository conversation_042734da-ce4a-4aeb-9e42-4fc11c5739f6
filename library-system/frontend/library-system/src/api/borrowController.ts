// @ts-ignore
/* eslint-disable */
import request from '@/request'

/** getBorrowRecords GET /api/borrow */
export async function getBorrowRecordsUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getBorrowRecordsUsingGETParams,
  options?: { [key: string]: any }
) {
  return request<API.ApiResponseIPageBorrowRecord_>('/api/borrow', {
    method: 'GET',
    params: {
      // current has a default value: 1
      current: '1',
      // size has a default value: 10
      size: '10',
      ...params,
    },
    ...(options || {}),
  })
}

/** borrowBook POST /api/borrow/borrow */
export async function borrowBookUsingPost(
  body: Record<string, any>,
  options?: { [key: string]: any }
) {
  return request<API.ApiResponseBorrowRecord_>('/api/borrow/borrow', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** canReaderBorrowMore GET /api/borrow/can-borrow/${param0} */
export async function canReaderBorrowMoreUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.canReaderBorrowMoreUsingGETParams,
  options?: { [key: string]: any }
) {
  const { readerId: param0, ...queryParams } = params
  return request<API.ApiResponseBoolean_>(`/api/borrow/can-borrow/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** getReaderCurrentBorrowCount GET /api/borrow/count/${param0} */
export async function getReaderCurrentBorrowCountUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getReaderCurrentBorrowCountUsingGETParams,
  options?: { [key: string]: any }
) {
  const { readerId: param0, ...queryParams } = params
  return request<API.ApiResponseInt_>(`/api/borrow/count/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** getCurrentBorrowsByReader GET /api/borrow/current/${param0} */
export async function getCurrentBorrowsByReaderUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCurrentBorrowsByReaderUsingGETParams,
  options?: { [key: string]: any }
) {
  const { readerId: param0, ...queryParams } = params
  return request<API.ApiResponseListBorrowRecord_>(`/api/borrow/current/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** getRecordsDueSoon GET /api/borrow/due-soon */
export async function getRecordsDueSoonUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getRecordsDueSoonUsingGETParams,
  options?: { [key: string]: any }
) {
  return request<API.ApiResponseListBorrowRecord_>('/api/borrow/due-soon', {
    method: 'GET',
    params: {
      // days has a default value: 3
      days: '3',
      ...params,
    },
    ...(options || {}),
  })
}

/** calculateFine GET /api/borrow/fine/${param0} */
export async function calculateFineUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.calculateFineUsingGETParams,
  options?: { [key: string]: any }
) {
  const { recordId: param0, ...queryParams } = params
  return request<API.ApiResponseBigdecimal_>(`/api/borrow/fine/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** getBorrowHistoryByReader GET /api/borrow/history/${param0} */
export async function getBorrowHistoryByReaderUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getBorrowHistoryByReaderUsingGETParams,
  options?: { [key: string]: any }
) {
  const { readerId: param0, ...queryParams } = params
  return request<API.ApiResponseIPageBorrowRecord_>(`/api/borrow/history/${param0}`, {
    method: 'GET',
    params: {
      // current has a default value: 1
      current: '1',
      // size has a default value: 10
      size: '10',
      ...queryParams,
    },
    ...(options || {}),
  })
}

/** getOverdueRecords GET /api/borrow/overdue */
export async function getOverdueRecordsUsingGet(options?: { [key: string]: any }) {
  return request<API.ApiResponseListBorrowRecord_>('/api/borrow/overdue', {
    method: 'GET',
    ...(options || {}),
  })
}

/** processOverdueRecords POST /api/borrow/process-overdue */
export async function processOverdueRecordsUsingPost(options?: { [key: string]: any }) {
  return request<API.ApiResponseInt_>('/api/borrow/process-overdue', {
    method: 'POST',
    ...(options || {}),
  })
}

/** getBorrowRecordById GET /api/borrow/record/${param0} */
export async function getBorrowRecordByIdUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getBorrowRecordByIdUsingGETParams,
  options?: { [key: string]: any }
) {
  const { recordId: param0, ...queryParams } = params
  return request<API.ApiResponseBorrowRecord_>(`/api/borrow/record/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** renewBook POST /api/borrow/renew/${param0} */
export async function renewBookUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.renewBookUsingPOSTParams,
  options?: { [key: string]: any }
) {
  const { recordId: param0, ...queryParams } = params
  return request<API.ApiResponseBorrowRecord_>(`/api/borrow/renew/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** returnBook POST /api/borrow/return/${param0} */
export async function returnBookUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.returnBookUsingPOSTParams,
  options?: { [key: string]: any }
) {
  const { recordId: param0, ...queryParams } = params
  return request<API.ApiResponseBorrowRecord_>(`/api/borrow/return/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** searchBorrowRecords GET /api/borrow/search */
export async function searchBorrowRecordsUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.searchBorrowRecordsUsingGETParams,
  options?: { [key: string]: any }
) {
  return request<API.ApiResponseIPageBorrowRecord_>('/api/borrow/search', {
    method: 'GET',
    params: {
      // current has a default value: 1
      current: '1',

      // size has a default value: 10
      size: '10',

      ...params,
    },
    ...(options || {}),
  })
}

/** getBorrowingStatistics GET /api/borrow/statistics */
export async function getBorrowingStatisticsUsingGet(options?: { [key: string]: any }) {
  return request<API.ApiResponseMapStringObject_>('/api/borrow/statistics', {
    method: 'GET',
    ...(options || {}),
  })
}

/** getMonthlyBorrowingStats GET /api/borrow/statistics/monthly/${param0} */
export async function getMonthlyBorrowingStatsUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMonthlyBorrowingStatsUsingGETParams,
  options?: { [key: string]: any }
) {
  const { year: param0, ...queryParams } = params
  return request<API.ApiResponseListMapStringObject_>(`/api/borrow/statistics/monthly/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  })
}

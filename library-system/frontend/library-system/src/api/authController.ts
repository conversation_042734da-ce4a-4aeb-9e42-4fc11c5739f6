// @ts-ignore
/* eslint-disable */
import request from '@/request'

/** changePassword POST /api/auth/change-password */
export async function changePasswordUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.changePasswordUsingPOSTParams,
  body: Record<string, any>,
  options?: { [key: string]: any }
) {
  return request<API.ApiResponseVoid_>('/api/auth/change-password', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  })
}

/** checkLogin GET /api/auth/check-login */
export async function checkLoginUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.checkLoginUsingGETParams,
  options?: { [key: string]: any }
) {
  return request<API.ApiResponseBoolean_>('/api/auth/check-login', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  })
}

/** getCurrentUser GET /api/auth/current-user */
export async function getCurrentUserUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCurrentUserUsingGETParams,
  options?: { [key: string]: any }
) {
  return request<API.ApiResponseMapStringObject_>('/api/auth/current-user', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  })
}

/** login POST /api/auth/login */
export async function loginUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.loginUsingPOSTParams,
  body: Record<string, any>,
  options?: { [key: string]: any }
) {
  return request<API.ApiResponseMapStringObject_>('/api/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  })
}

/** logout POST /api/auth/logout */
export async function logoutUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.logoutUsingPOSTParams,
  options?: { [key: string]: any }
) {
  return request<API.ApiResponseVoid_>('/api/auth/logout', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  })
}

/** register POST /api/auth/register */
export async function registerUsingPost(body: API.User, options?: { [key: string]: any }) {
  return request<API.ApiResponseMapStringObject_>('/api/auth/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

declare namespace API {
  type ApiResponseBigdecimal_ = {
    code?: string
    data?: number
    message?: string
    success?: boolean
  }

  type ApiResponseBook_ = {
    code?: string
    data?: Book
    message?: string
    success?: boolean
  }

  type ApiResponseBoolean_ = {
    code?: string
    data?: boolean
    message?: string
    success?: boolean
  }

  type ApiResponseBorrowRecord_ = {
    code?: string
    data?: BorrowRecord
    message?: string
    success?: boolean
  }

  type ApiResponseInt_ = {
    code?: string
    data?: number
    message?: string
    success?: boolean
  }

  type ApiResponseIPageBook_ = {
    code?: string
    data?: IPageBook_
    message?: string
    success?: boolean
  }

  type ApiResponseIPageBorrowRecord_ = {
    code?: string
    data?: IPageBorrowRecord_
    message?: string
    success?: boolean
  }

  type ApiResponseIPageUser_ = {
    code?: string
    data?: IPageUser_
    message?: string
    success?: boolean
  }

  type ApiResponseListBook_ = {
    code?: string
    data?: Book[]
    message?: string
    success?: boolean
  }

  type ApiResponseListBorrowRecord_ = {
    code?: string
    data?: BorrowRecord[]
    message?: string
    success?: boolean
  }

  type ApiResponseListMapStringObject_ = {
    code?: string
    data?: MapStringObject_[]
    message?: string
    success?: boolean
  }

  type ApiResponseListUser_ = {
    code?: string
    data?: User[]
    message?: string
    success?: boolean
  }

  type ApiResponseMapStringObject_ = {
    code?: string
    data?: Record<string, any>
    message?: string
    success?: boolean
  }

  type ApiResponseString_ = {
    code?: string
    data?: string
    message?: string
    success?: boolean
  }

  type ApiResponseUser_ = {
    code?: string
    data?: User
    message?: string
    success?: boolean
  }

  type ApiResponseVoid_ = {
    code?: string
    message?: string
    success?: boolean
  }

  type Book = {
    author?: string
    availableCount?: number
    availableForBorrow?: boolean
    categoryId?: number
    createdAt?: string
    id?: number
    isbn?: string
    publisher?: string
    status?: string
    title?: string
    totalCount?: number
    updatedAt?: string
  }

  type Book1 = {
    author?: string
    availableCount?: number
    categoryId?: number
    createdAt?: string
    id?: number
    isbn?: string
    publisher?: string
    status?: string
    title?: string
    totalCount?: number
    updatedAt?: string
  }

  type BorrowRecord = {
    bookId?: number
    borrowDate?: string
    createdAt?: string
    dueDate?: string
    fineAmount?: number
    id?: number
    notes?: string
    overdue?: boolean
    overdueDays?: number
    readerId?: number
    renewalCount?: number
    returnDate?: string
    status?: string
    updatedAt?: string
  }

  type calculateFineUsingGETParams = {
    /** recordId */
    recordId: number
  }

  type canReaderBorrowMoreUsingGETParams = {
    /** readerId */
    readerId: number
  }

  type changePasswordUsingPOST1Params = {
    /** id */
    id: number
  }

  type changePasswordUsingPOSTParams = {
    creationTime?: number
    id?: string
    lastAccessedTime?: number
    maxInactiveInterval?: number
    new?: boolean
    'servletContext.classLoader'?: any
    'servletContext.contextPath'?: string
    'servletContext.defaultSessionTrackingModes'?: 'COOKIE' | 'URL' | 'SSL'
    'servletContext.effectiveMajorVersion'?: number
    'servletContext.effectiveMinorVersion'?: number
    'servletContext.effectiveSessionTrackingModes'?: 'COOKIE' | 'URL' | 'SSL'
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas'?: string[]
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes'?: string[]
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns'?: string[]
    'servletContext.jspConfigDescriptor.taglibs[0].taglibLocation'?: string
    'servletContext.jspConfigDescriptor.taglibs[0].taglibURI'?: string
    'servletContext.majorVersion'?: number
    'servletContext.minorVersion'?: number
    'servletContext.requestCharacterEncoding'?: string
    'servletContext.responseCharacterEncoding'?: string
    'servletContext.serverInfo'?: string
    'servletContext.servletContextName'?: string
    'servletContext.sessionCookieConfig.comment'?: string
    'servletContext.sessionCookieConfig.domain'?: string
    'servletContext.sessionCookieConfig.httpOnly'?: boolean
    'servletContext.sessionCookieConfig.maxAge'?: number
    'servletContext.sessionCookieConfig.name'?: string
    'servletContext.sessionCookieConfig.path'?: string
    'servletContext.sessionCookieConfig.secure'?: boolean
    'servletContext.sessionTimeout'?: number
    'servletContext.virtualServerName'?: string
    valueNames?: string[]
  }

  type checkIsbnUsingGETParams = {
    /** excludeBookId */
    excludeBookId?: number
    /** isbn */
    isbn: string
  }

  type checkLoginUsingGETParams = {
    creationTime?: number
    id?: string
    lastAccessedTime?: number
    maxInactiveInterval?: number
    new?: boolean
    'servletContext.classLoader'?: any
    'servletContext.contextPath'?: string
    'servletContext.defaultSessionTrackingModes'?: 'COOKIE' | 'URL' | 'SSL'
    'servletContext.effectiveMajorVersion'?: number
    'servletContext.effectiveMinorVersion'?: number
    'servletContext.effectiveSessionTrackingModes'?: 'COOKIE' | 'URL' | 'SSL'
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas'?: string[]
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes'?: string[]
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns'?: string[]
    'servletContext.jspConfigDescriptor.taglibs[0].taglibLocation'?: string
    'servletContext.jspConfigDescriptor.taglibs[0].taglibURI'?: string
    'servletContext.majorVersion'?: number
    'servletContext.minorVersion'?: number
    'servletContext.requestCharacterEncoding'?: string
    'servletContext.responseCharacterEncoding'?: string
    'servletContext.serverInfo'?: string
    'servletContext.servletContextName'?: string
    'servletContext.sessionCookieConfig.comment'?: string
    'servletContext.sessionCookieConfig.domain'?: string
    'servletContext.sessionCookieConfig.httpOnly'?: boolean
    'servletContext.sessionCookieConfig.maxAge'?: number
    'servletContext.sessionCookieConfig.name'?: string
    'servletContext.sessionCookieConfig.path'?: string
    'servletContext.sessionCookieConfig.secure'?: boolean
    'servletContext.sessionTimeout'?: number
    'servletContext.virtualServerName'?: string
    valueNames?: string[]
  }

  type checkUsernameUsingGETParams = {
    /** excludeUserId */
    excludeUserId?: number
    /** username */
    username: string
  }

  type deleteBookUsingDELETEParams = {
    /** id */
    id: number
  }

  type deleteUserUsingDELETEParams = {
    /** id */
    id: number
  }

  type getBookByIdUsingGETParams = {
    /** id */
    id: number
  }

  type getBookByIsbnUsingGETParams = {
    /** isbn */
    isbn: string
  }

  type getBooksByCategoryUsingGETParams = {
    /** categoryId */
    categoryId: number
  }

  type getBooksUsingGETParams = {
    /** current */
    current?: number
    /** size */
    size?: number
  }

  type getBorrowHistoryByReaderUsingGETParams = {
    /** current */
    current?: number
    /** readerId */
    readerId: number
    /** size */
    size?: number
  }

  type getBorrowRecordByIdUsingGETParams = {
    /** recordId */
    recordId: number
  }

  type getBorrowRecordsUsingGETParams = {
    /** current */
    current?: number
    /** size */
    size?: number
  }

  type getCurrentBorrowsByReaderUsingGETParams = {
    /** readerId */
    readerId: number
  }

  type getCurrentUserUsingGETParams = {
    creationTime?: number
    id?: string
    lastAccessedTime?: number
    maxInactiveInterval?: number
    new?: boolean
    'servletContext.classLoader'?: any
    'servletContext.contextPath'?: string
    'servletContext.defaultSessionTrackingModes'?: 'COOKIE' | 'URL' | 'SSL'
    'servletContext.effectiveMajorVersion'?: number
    'servletContext.effectiveMinorVersion'?: number
    'servletContext.effectiveSessionTrackingModes'?: 'COOKIE' | 'URL' | 'SSL'
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas'?: string[]
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes'?: string[]
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns'?: string[]
    'servletContext.jspConfigDescriptor.taglibs[0].taglibLocation'?: string
    'servletContext.jspConfigDescriptor.taglibs[0].taglibURI'?: string
    'servletContext.majorVersion'?: number
    'servletContext.minorVersion'?: number
    'servletContext.requestCharacterEncoding'?: string
    'servletContext.responseCharacterEncoding'?: string
    'servletContext.serverInfo'?: string
    'servletContext.servletContextName'?: string
    'servletContext.sessionCookieConfig.comment'?: string
    'servletContext.sessionCookieConfig.domain'?: string
    'servletContext.sessionCookieConfig.httpOnly'?: boolean
    'servletContext.sessionCookieConfig.maxAge'?: number
    'servletContext.sessionCookieConfig.name'?: string
    'servletContext.sessionCookieConfig.path'?: string
    'servletContext.sessionCookieConfig.secure'?: boolean
    'servletContext.sessionTimeout'?: number
    'servletContext.virtualServerName'?: string
    valueNames?: string[]
  }

  type getLoginUsingGETParams = {
    /** roleId */
    roleId: string
    /** userId */
    userId: string
  }

  type getMonthlyBorrowingStatsUsingGETParams = {
    /** year */
    year: number
  }

  type getReaderCurrentBorrowCountUsingGETParams = {
    /** readerId */
    readerId: number
  }

  type getRecordsDueSoonUsingGETParams = {
    /** days */
    days?: number
  }

  type getRegExpUsingGETParams = {
    /** regexp1 */
    regexp1: string
  }

  type getUserByIdUsingGETParams = {
    /** id */
    id: number
  }

  type getUserListUsingGETParams = {
    /** current */
    current?: number
    /** role */
    role?: string
    /** size */
    size?: number
    /** status */
    status?: string
  }

  type getUsersByRoleUsingGETParams = {
    /** role */
    role: string
  }

  type helloUsingDELETEParams = {
    /** name */
    name?: string
  }

  type helloUsingGETParams = {
    /** name */
    name?: string
  }

  type helloUsingPATCHParams = {
    /** name */
    name?: string
  }

  type helloUsingPOSTParams = {
    /** name */
    name?: string
  }

  type helloUsingPUTParams = {
    /** name */
    name?: string
  }

  type IPageBook_ = {
    current?: number
    pages?: number
    records?: Book[]
    size?: number
    total?: number
  }

  type IPageBorrowRecord_ = {
    current?: number
    pages?: number
    records?: BorrowRecord[]
    size?: number
    total?: number
  }

  type IPageUser_ = {
    current?: number
    pages?: number
    records?: User[]
    size?: number
    total?: number
  }

  type isBookAvailableForBorrowUsingGETParams = {
    /** id */
    id: number
  }

  type loginUsingPOSTParams = {
    creationTime?: number
    id?: string
    lastAccessedTime?: number
    maxInactiveInterval?: number
    new?: boolean
    'servletContext.classLoader'?: any
    'servletContext.contextPath'?: string
    'servletContext.defaultSessionTrackingModes'?: 'COOKIE' | 'URL' | 'SSL'
    'servletContext.effectiveMajorVersion'?: number
    'servletContext.effectiveMinorVersion'?: number
    'servletContext.effectiveSessionTrackingModes'?: 'COOKIE' | 'URL' | 'SSL'
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas'?: string[]
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes'?: string[]
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns'?: string[]
    'servletContext.jspConfigDescriptor.taglibs[0].taglibLocation'?: string
    'servletContext.jspConfigDescriptor.taglibs[0].taglibURI'?: string
    'servletContext.majorVersion'?: number
    'servletContext.minorVersion'?: number
    'servletContext.requestCharacterEncoding'?: string
    'servletContext.responseCharacterEncoding'?: string
    'servletContext.serverInfo'?: string
    'servletContext.servletContextName'?: string
    'servletContext.sessionCookieConfig.comment'?: string
    'servletContext.sessionCookieConfig.domain'?: string
    'servletContext.sessionCookieConfig.httpOnly'?: boolean
    'servletContext.sessionCookieConfig.maxAge'?: number
    'servletContext.sessionCookieConfig.name'?: string
    'servletContext.sessionCookieConfig.path'?: string
    'servletContext.sessionCookieConfig.secure'?: boolean
    'servletContext.sessionTimeout'?: number
    'servletContext.virtualServerName'?: string
    valueNames?: string[]
  }

  type logoutUsingPOSTParams = {
    creationTime?: number
    id?: string
    lastAccessedTime?: number
    maxInactiveInterval?: number
    new?: boolean
    'servletContext.classLoader'?: any
    'servletContext.contextPath'?: string
    'servletContext.defaultSessionTrackingModes'?: 'COOKIE' | 'URL' | 'SSL'
    'servletContext.effectiveMajorVersion'?: number
    'servletContext.effectiveMinorVersion'?: number
    'servletContext.effectiveSessionTrackingModes'?: 'COOKIE' | 'URL' | 'SSL'
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas'?: string[]
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes'?: string[]
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces'?: string
    'servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns'?: string[]
    'servletContext.jspConfigDescriptor.taglibs[0].taglibLocation'?: string
    'servletContext.jspConfigDescriptor.taglibs[0].taglibURI'?: string
    'servletContext.majorVersion'?: number
    'servletContext.minorVersion'?: number
    'servletContext.requestCharacterEncoding'?: string
    'servletContext.responseCharacterEncoding'?: string
    'servletContext.serverInfo'?: string
    'servletContext.servletContextName'?: string
    'servletContext.sessionCookieConfig.comment'?: string
    'servletContext.sessionCookieConfig.domain'?: string
    'servletContext.sessionCookieConfig.httpOnly'?: boolean
    'servletContext.sessionCookieConfig.maxAge'?: number
    'servletContext.sessionCookieConfig.name'?: string
    'servletContext.sessionCookieConfig.path'?: string
    'servletContext.sessionCookieConfig.secure'?: boolean
    'servletContext.sessionTimeout'?: number
    'servletContext.virtualServerName'?: string
    valueNames?: string[]
  }

  type MapStringObject_ = true

  type ModelAndView = {
    empty?: boolean
    model?: Record<string, any>
    modelMap?: Record<string, any>
    reference?: boolean
    status?:
      | 'CONTINUE'
      | 'SWITCHING_PROTOCOLS'
      | 'PROCESSING'
      | 'CHECKPOINT'
      | 'OK'
      | 'CREATED'
      | 'ACCEPTED'
      | 'NON_AUTHORITATIVE_INFORMATION'
      | 'NO_CONTENT'
      | 'RESET_CONTENT'
      | 'PARTIAL_CONTENT'
      | 'MULTI_STATUS'
      | 'ALREADY_REPORTED'
      | 'IM_USED'
      | 'MULTIPLE_CHOICES'
      | 'MOVED_PERMANENTLY'
      | 'FOUND'
      | 'MOVED_TEMPORARILY'
      | 'SEE_OTHER'
      | 'NOT_MODIFIED'
      | 'USE_PROXY'
      | 'TEMPORARY_REDIRECT'
      | 'PERMANENT_REDIRECT'
      | 'BAD_REQUEST'
      | 'UNAUTHORIZED'
      | 'PAYMENT_REQUIRED'
      | 'FORBIDDEN'
      | 'NOT_FOUND'
      | 'METHOD_NOT_ALLOWED'
      | 'NOT_ACCEPTABLE'
      | 'PROXY_AUTHENTICATION_REQUIRED'
      | 'REQUEST_TIMEOUT'
      | 'CONFLICT'
      | 'GONE'
      | 'LENGTH_REQUIRED'
      | 'PRECONDITION_FAILED'
      | 'PAYLOAD_TOO_LARGE'
      | 'REQUEST_ENTITY_TOO_LARGE'
      | 'URI_TOO_LONG'
      | 'REQUEST_URI_TOO_LONG'
      | 'UNSUPPORTED_MEDIA_TYPE'
      | 'REQUESTED_RANGE_NOT_SATISFIABLE'
      | 'EXPECTATION_FAILED'
      | 'I_AM_A_TEAPOT'
      | 'INSUFFICIENT_SPACE_ON_RESOURCE'
      | 'METHOD_FAILURE'
      | 'DESTINATION_LOCKED'
      | 'UNPROCESSABLE_ENTITY'
      | 'LOCKED'
      | 'FAILED_DEPENDENCY'
      | 'TOO_EARLY'
      | 'UPGRADE_REQUIRED'
      | 'PRECONDITION_REQUIRED'
      | 'TOO_MANY_REQUESTS'
      | 'REQUEST_HEADER_FIELDS_TOO_LARGE'
      | 'UNAVAILABLE_FOR_LEGAL_REASONS'
      | 'INTERNAL_SERVER_ERROR'
      | 'NOT_IMPLEMENTED'
      | 'BAD_GATEWAY'
      | 'SERVICE_UNAVAILABLE'
      | 'GATEWAY_TIMEOUT'
      | 'HTTP_VERSION_NOT_SUPPORTED'
      | 'VARIANT_ALSO_NEGOTIATES'
      | 'INSUFFICIENT_STORAGE'
      | 'LOOP_DETECTED'
      | 'BANDWIDTH_LIMIT_EXCEEDED'
      | 'NOT_EXTENDED'
      | 'NETWORK_AUTHENTICATION_REQUIRED'
    view?: View
    viewName?: string
  }

  type renewBookUsingPOSTParams = {
    /** recordId */
    recordId: number
  }

  type resetPasswordUsingPOSTParams = {
    /** id */
    id: number
  }

  type returnBookUsingPOSTParams = {
    /** recordId */
    recordId: number
  }

  type saveUserUsingDELETEParams = {
    age?: number
    name?: string
  }

  type saveUserUsingGETParams = {
    age?: number
    name?: string
  }

  type saveUserUsingPATCHParams = {
    age?: number
    name?: string
  }

  type saveUserUsingPOSTParams = {
    age?: number
    name?: string
  }

  type saveUserUsingPUTParams = {
    age?: number
    name?: string
  }

  type searchBooksUsingGETParams = {
    /** author */
    author?: string
    /** categoryId */
    categoryId?: number
    /** current */
    current?: number
    /** size */
    size?: number
    /** status */
    status?: string
    /** title */
    title?: string
  }

  type searchBorrowRecordsUsingGETParams = {
    /** bookId */
    bookId?: number
    /** current */
    current?: number
    /** endDate */
    endDate?: string
    /** readerId */
    readerId?: number
    /** size */
    size?: number
    /** startDate */
    startDate?: string
    /** status */
    status?: string
  }

  type updateBookInventoryUsingPOSTParams = {
    /** id */
    id: number
  }

  type updateBookStatusUsingPOSTParams = {
    /** id */
    id: number
  }

  type updateBookUsingPUTParams = {
    /** id */
    id: number
  }

  type updateUserStatusUsingPOSTParams = {
    /** id */
    id: number
  }

  type updateUserUsingPUTParams = {
    /** id */
    id: number
  }

  type User = {
    createdAt?: string
    id?: number
    password?: string
    role?: string
    status?: string
    updatedAt?: string
    username?: string
  }

  type User1 = {
    age?: number
    name?: string
  }

  type View = {
    contentType?: string
  }
}

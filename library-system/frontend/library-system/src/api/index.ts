import axios from 'axios'
import { message } from 'ant-design-vue'

// API Response interface
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  code?: string
}

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
  withCredentials: true, // Re-enabled after backend CORS configuration update
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
api.interceptors.request.use(
  (config: any) => {
    // Add auth token if available
    const token = localStorage.getItem('library_token')
    if (token && config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    
    // Add timestamp to prevent caching
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response: any) => {
    const { data } = response
    
    // Handle successful responses
    if (data.success) {
      return response
    } else {
      // Handle business logic errors
      message.error(data.message || 'Operation failed')
      return Promise.reject(new Error(data.message || 'Operation failed'))
    }
  },
  (error) => {
    console.error('Response error:', error)
    
    // Handle different error types
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          localStorage.removeItem('library_token')
          localStorage.removeItem('library_user')
          message.error('Session expired. Please login again.')
          window.location.href = '/login'
          break
        case 403:
          message.error('Access denied')
          break
        case 404:
          message.error('Resource not found')
          break
        case 500:
          message.error('Server error. Please try again later.')
          break
        default:
          message.error(data?.message || `Error ${status}`)
      }
    } else if (error.request) {
      // Network error
      message.error('Network error. Please check your connection.')
    } else {
      // Other errors
      message.error(error.message || 'An unexpected error occurred')
    }
    
    return Promise.reject(error)
  }
)

// Generic API methods
export const apiClient = {
  get: <T = any>(url: string, config?: any): Promise<any> =>
    api.get(url, config),

  post: <T = any>(url: string, data?: any, config?: any): Promise<any> =>
    api.post(url, data, config),

  put: <T = any>(url: string, data?: any, config?: any): Promise<any> =>
    api.put(url, data, config),

  delete: <T = any>(url: string, config?: any): Promise<any> =>
    api.delete(url, config),

  patch: <T = any>(url: string, data?: any, config?: any): Promise<any> =>
    api.patch(url, data, config)
}

export default api

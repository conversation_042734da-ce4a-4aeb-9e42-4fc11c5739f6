// @ts-ignore
/* eslint-disable */
import request from '@/request'

/** getUserList GET /api/users */
export async function getUserListUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getUserListUsingGETParams,
  options?: { [key: string]: any }
) {
  return request<API.ApiResponseIPageUser_>('/api/users', {
    method: 'GET',
    params: {
      // current has a default value: 1
      current: '1',

      // size has a default value: 10
      size: '10',
      ...params,
    },
    ...(options || {}),
  })
}

/** createUser POST /api/users */
export async function createUserUsingPost(body: API.User, options?: { [key: string]: any }) {
  return request<API.ApiResponseUser_>('/api/users', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** getUserById GET /api/users/${param0} */
export async function getUserByIdUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getUserByIdUsingGETParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params
  return request<API.ApiResponseUser_>(`/api/users/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** updateUser PUT /api/users/${param0} */
export async function updateUserUsingPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUserUsingPUTParams,
  body: API.User,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params
  return request<API.ApiResponseUser_>(`/api/users/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  })
}

/** deleteUser DELETE /api/users/${param0} */
export async function deleteUserUsingDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUserUsingDELETEParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params
  return request<API.ApiResponseVoid_>(`/api/users/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** changePassword POST /api/users/${param0}/change-password */
export async function changePasswordUsingPost1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.changePasswordUsingPOST1Params,
  body: Record<string, any>,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params
  return request<API.ApiResponseVoid_>(`/api/users/${param0}/change-password`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  })
}

/** resetPassword POST /api/users/${param0}/reset-password */
export async function resetPasswordUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.resetPasswordUsingPOSTParams,
  body: Record<string, any>,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params
  return request<API.ApiResponseVoid_>(`/api/users/${param0}/reset-password`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  })
}

/** updateUserStatus POST /api/users/${param0}/status */
export async function updateUserStatusUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUserStatusUsingPOSTParams,
  body: Record<string, any>,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params
  return request<API.ApiResponseVoid_>(`/api/users/${param0}/status`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  })
}

/** checkUsername GET /api/users/check-username */
export async function checkUsernameUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.checkUsernameUsingGETParams,
  options?: { [key: string]: any }
) {
  return request<API.ApiResponseBoolean_>('/api/users/check-username', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  })
}

/** getUsersByRole GET /api/users/role/${param0} */
export async function getUsersByRoleUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getUsersByRoleUsingGETParams,
  options?: { [key: string]: any }
) {
  const { role: param0, ...queryParams } = params
  return request<API.ApiResponseListUser_>(`/api/users/role/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  })
}

// @ts-ignore
/* eslint-disable */
import request from '@/request'

/** healthCheck GET /api/health */
export async function healthCheckUsingGet(options?: { [key: string]: any }) {
  return request<API.ApiResponseMapStringObject_>('/api/health', {
    method: 'GET',
    ...(options || {}),
  })
}

/** ping GET /api/health/ping */
export async function pingUsingGet(options?: { [key: string]: any }) {
  return request<API.ApiResponseString_>('/api/health/ping', {
    method: 'GET',
    ...(options || {}),
  })
}

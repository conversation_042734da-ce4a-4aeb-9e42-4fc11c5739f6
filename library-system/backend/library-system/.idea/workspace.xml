<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5bbb86bb-9081-4ca1-b96d-c3f48b8c4ec6" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/org/cxw/librarysystem/LibrarySystemApplication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/org/cxw/librarysystem/config/CorsConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/org/cxw/librarysystem/demos/web/BasicController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/org/cxw/librarysystem/demos/web/PathVariableController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/org/cxw/librarysystem/demos/web/User.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/resources/static/index.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/org/cxw/librarysystem/LibrarySystemApplicationTests.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 8
}]]></component>
  <component name="ProjectId" id="30o39if61eCReOhA5Bax0XlI3AB" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.LibrarySystemApplication.executor": "Run",
    "git-widget-placeholder": "main",
    "kotlin-language-version-configured": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "reference.projectsettings.compiler.javacompiler",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="org.cxw.librarysystem.config" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="LibrarySystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="library-system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.cxw.librarysystem.LibrarySystemApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.cxw.librarysystem.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.LibrarySystemApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.18034.62" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.18034.62" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="5bbb86bb-9081-4ca1-b96d-c3f48b8c4ec6" name="Changes" comment="" />
      <created>1754279950564</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754279950564</updated>
      <workItem from="1754279952104" duration="18776000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>